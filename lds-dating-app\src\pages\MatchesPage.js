import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { FiHeart, FiMessageCircle, FiMapPin } from 'react-icons/fi';
import { useMatch } from '../contexts/MatchContext';
import { useAuth } from '../contexts/AuthContext';
import Card from '../components/UI/Card';
import Avatar from '../components/UI/Avatar';
import Button from '../components/UI/Button';

const MatchesContainer = styled.div`
  max-width: 800px;
  margin: 0 auto;
`;

const Header = styled.div`
  text-align: center;
  margin-bottom: ${props => props.theme.spacing[8]};
`;

const Title = styled.h1`
  font-size: ${props => props.theme.fontSizes['2xl']};
  font-weight: ${props => props.theme.fontWeights.bold};
  color: ${props => props.theme.colors.text.primary};
  margin-bottom: ${props => props.theme.spacing[2]};
`;

const Subtitle = styled.p`
  color: ${props => props.theme.colors.text.secondary};
`;

const MatchesGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: ${props => props.theme.spacing[6]};
`;

const MatchCard = styled(Card)`
  padding: ${props => props.theme.spacing[6]};
  text-align: center;
  transition: transform 0.2s ease;
  
  &:hover {
    transform: translateY(-4px);
  }
`;

const MatchAvatar = styled.div`
  margin-bottom: ${props => props.theme.spacing[4]};
`;

const MatchName = styled.h3`
  font-size: ${props => props.theme.fontSizes.lg};
  font-weight: ${props => props.theme.fontWeights.semibold};
  color: ${props => props.theme.colors.text.primary};
  margin-bottom: ${props => props.theme.spacing[1]};
`;

const MatchLocation = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  gap: ${props => props.theme.spacing[1]};
  color: ${props => props.theme.colors.text.secondary};
  font-size: ${props => props.theme.fontSizes.sm};
  margin-bottom: ${props => props.theme.spacing[4]};
`;

const MatchDate = styled.p`
  color: ${props => props.theme.colors.text.secondary};
  font-size: ${props => props.theme.fontSizes.sm};
  margin-bottom: ${props => props.theme.spacing[4]};
`;

const EmptyState = styled.div`
  text-align: center;
  padding: ${props => props.theme.spacing[12]} ${props => props.theme.spacing[6]};
`;

const EmptyStateIcon = styled.div`
  font-size: ${props => props.theme.fontSizes['4xl']};
  color: ${props => props.theme.colors.text.secondary};
  margin-bottom: ${props => props.theme.spacing[4]};
`;

const EmptyStateTitle = styled.h3`
  font-size: ${props => props.theme.fontSizes.xl};
  font-weight: ${props => props.theme.fontWeights.semibold};
  color: ${props => props.theme.colors.text.primary};
  margin-bottom: ${props => props.theme.spacing[2]};
`;

const EmptyStateText = styled.p`
  color: ${props => props.theme.colors.text.secondary};
  line-height: 1.6;
`;

const MatchesPage = () => {
  const [matches, setMatches] = useState([]);
  const [loading, setLoading] = useState(true);
  const { getUserMatches } = useMatch();
  const { getUserProfile } = useAuth();

  useEffect(() => {
    loadMatches();
  }, []);

  const loadMatches = async () => {
    try {
      setLoading(true);
      const matchesData = await getUserMatches();
      
      // Get profile data for each match
      const matchesWithProfiles = await Promise.all(
        matchesData.map(async (match) => {
          const otherUserId = match.users.find(id => id !== getUserProfile()?.uid);
          const otherUserProfile = await getUserProfile(otherUserId);
          return {
            ...match,
            otherUser: otherUserProfile
          };
        })
      );
      
      setMatches(matchesWithProfiles);
    } catch (error) {
      console.error('Error loading matches:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <MatchesContainer>
        <Header>
          <Title>Loading matches...</Title>
        </Header>
      </MatchesContainer>
    );
  }

  return (
    <MatchesContainer>
      <Header>
        <Title>Your Matches</Title>
        <Subtitle>People who liked you back</Subtitle>
      </Header>

      {matches.length > 0 ? (
        <MatchesGrid>
          {matches.map((match) => (
            <MatchCard key={match.id}>
              <MatchAvatar>
                <Avatar
                  src={match.otherUser?.photos?.[0]}
                  name={match.otherUser?.displayName}
                  size="xl"
                />
              </MatchAvatar>
              
              <MatchName>{match.otherUser?.displayName}</MatchName>
              
              <MatchLocation>
                <FiMapPin />
                {match.otherUser?.city}, {match.otherUser?.country}
              </MatchLocation>
              
              <MatchDate>
                Matched on {new Date(match.createdAt?.toDate()).toLocaleDateString()}
              </MatchDate>
              
              <Button fullWidth>
                <FiMessageCircle /> Send Message
              </Button>
            </MatchCard>
          ))}
        </MatchesGrid>
      ) : (
        <EmptyState>
          <EmptyStateIcon>💕</EmptyStateIcon>
          <EmptyStateTitle>No Matches Yet</EmptyStateTitle>
          <EmptyStateText>
            Keep swiping to find your perfect match! When someone likes you back, 
            they'll appear here and you can start chatting.
          </EmptyStateText>
        </EmptyState>
      )}
    </MatchesContainer>
  );
};

export default MatchesPage;
