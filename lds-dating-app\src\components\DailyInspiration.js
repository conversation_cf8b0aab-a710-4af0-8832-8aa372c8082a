import React from 'react';
import styled from 'styled-components';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, FiBook } from 'react-icons/fi';
import { getDailyInspiration, getRandomQuote } from '../data/ldsContent';
import Card from './UI/Card';

const InspirationCard = styled(Card)`
  padding: ${props => props.theme.spacing[6]};
  background: linear-gradient(135deg, ${props => props.theme.colors.primary}10, ${props => props.theme.colors.accent}10);
  border-left: 4px solid ${props => props.theme.colors.primary};
  margin-bottom: ${props => props.theme.spacing[6]};
`;

const InspirationHeader = styled.div`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing[3]};
  margin-bottom: ${props => props.theme.spacing[4]};
`;

const InspirationIcon = styled.div`
  width: 40px;
  height: 40px;
  background: linear-gradient(45deg, ${props => props.theme.colors.primary}, ${props => props.theme.colors.accent});
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: ${props => props.theme.colors.white};
  font-size: ${props => props.theme.fontSizes.lg};
`;

const InspirationTitle = styled.h3`
  font-size: ${props => props.theme.fontSizes.lg};
  font-weight: ${props => props.theme.fontWeights.semibold};
  color: ${props => props.theme.colors.text.primary};
  margin: 0;
`;

const InspirationContent = styled.p`
  color: ${props => props.theme.colors.text.secondary};
  line-height: 1.6;
  margin-bottom: ${props => props.theme.spacing[4]};
`;

const ScriptureQuote = styled.div`
  background: ${props => props.theme.colors.white};
  padding: ${props => props.theme.spacing[4]};
  border-radius: ${props => props.theme.borderRadius.lg};
  border-left: 3px solid ${props => props.theme.colors.accent};
`;

const ScriptureText = styled.p`
  font-style: italic;
  color: ${props => props.theme.colors.text.secondary};
  margin-bottom: ${props => props.theme.spacing[2]};
  line-height: 1.5;
`;

const QuoteCard = styled(Card)`
  padding: ${props => props.theme.spacing[4]};
  text-align: center;
  background: ${props => props.theme.colors.white};
  border: 1px solid ${props => props.theme.colors.border.light};
`;

const QuoteText = styled.p`
  font-style: italic;
  color: ${props => props.theme.colors.text.secondary};
  margin-bottom: ${props => props.theme.spacing[2]};
  font-size: ${props => props.theme.fontSizes.base};
  line-height: 1.5;
`;

const QuoteReference = styled.p`
  font-size: ${props => props.theme.fontSizes.sm};
  color: ${props => props.theme.colors.text.secondary};
  font-weight: ${props => props.theme.fontWeights.semibold};
  margin: 0;
`;

const DailyInspiration = ({ showQuote = true, compact = false }) => {
  const inspiration = getDailyInspiration();
  const randomQuote = getRandomQuote();

  if (compact) {
    return (
      <QuoteCard>
        <QuoteText>"{randomQuote.text}"</QuoteText>
        <QuoteReference>— {randomQuote.reference}</QuoteReference>
      </QuoteCard>
    );
  }

  return (
    <>
      <InspirationCard>
        <InspirationHeader>
          <InspirationIcon>
            <FiSun />
          </InspirationIcon>
          <InspirationTitle>Daily Inspiration</InspirationTitle>
        </InspirationHeader>
        
        <InspirationContent>
          {inspiration.content}
        </InspirationContent>
        
        <ScriptureQuote>
          <ScriptureText>{inspiration.scripture}</ScriptureText>
        </ScriptureQuote>
      </InspirationCard>

      {showQuote && (
        <QuoteCard>
          <QuoteText>"{randomQuote.text}"</QuoteText>
          <QuoteReference>— {randomQuote.reference}</QuoteReference>
        </QuoteCard>
      )}
    </>
  );
};

export default DailyInspiration;
