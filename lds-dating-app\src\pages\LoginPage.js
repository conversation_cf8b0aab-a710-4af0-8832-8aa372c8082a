import React, { useState } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import { FiMail, <PERSON>Lock, <PERSON><PERSON>ye, FiEyeOff } from 'react-icons/fi';
import { useAuth } from '../contexts/AuthContext';
import Button from '../components/UI/Button';
import Input from '../components/UI/Input';
import Card from '../components/UI/Card';

const LoginContainer = styled.div`
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: ${props => props.theme.spacing[4]};
`;

const LoginCard = styled(Card)`
  width: 100%;
  max-width: 400px;
  padding: ${props => props.theme.spacing[8]};
`;

const Logo = styled.h1`
  font-size: ${props => props.theme.fontSizes['3xl']};
  font-weight: ${props => props.theme.fontWeights.bold};
  background: linear-gradient(45deg, ${props => props.theme.colors.primary}, ${props => props.theme.colors.accent});
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-align: center;
  margin-bottom: ${props => props.theme.spacing[2]};
`;

const Subtitle = styled.p`
  text-align: center;
  color: ${props => props.theme.colors.text.secondary};
  margin-bottom: ${props => props.theme.spacing[8]};
`;

const Form = styled.form`
  display: flex;
  flex-direction: column;
  gap: ${props => props.theme.spacing[4]};
`;

const PasswordInputWrapper = styled.div`
  position: relative;
`;

const PasswordToggle = styled.button`
  position: absolute;
  right: ${props => props.theme.spacing[3]};
  top: 50%;
  transform: translateY(-50%);
  color: ${props => props.theme.colors.text.secondary};
  background: none;
  border: none;
  cursor: pointer;
  padding: ${props => props.theme.spacing[1]};
  
  &:hover {
    color: ${props => props.theme.colors.text.primary};
  }
`;

const ForgotPassword = styled(Link)`
  color: ${props => props.theme.colors.primary};
  font-size: ${props => props.theme.fontSizes.sm};
  text-align: right;
  margin-top: -${props => props.theme.spacing[2]};
  margin-bottom: ${props => props.theme.spacing[2]};
  
  &:hover {
    text-decoration: underline;
  }
`;

const Divider = styled.div`
  display: flex;
  align-items: center;
  margin: ${props => props.theme.spacing[6]} 0;
  
  &::before,
  &::after {
    content: '';
    flex: 1;
    height: 1px;
    background: ${props => props.theme.colors.border.medium};
  }
  
  span {
    padding: 0 ${props => props.theme.spacing[4]};
    color: ${props => props.theme.colors.text.secondary};
    font-size: ${props => props.theme.fontSizes.sm};
  }
`;

const SignUpLink = styled.div`
  text-align: center;
  color: ${props => props.theme.colors.text.secondary};
  
  a {
    color: ${props => props.theme.colors.primary};
    font-weight: ${props => props.theme.fontWeights.semibold};
    
    &:hover {
      text-decoration: underline;
    }
  }
`;

const Quote = styled.div`
  text-align: center;
  margin-top: ${props => props.theme.spacing[6]};
  padding: ${props => props.theme.spacing[4]};
  background: ${props => props.theme.colors.gray[50]};
  border-radius: ${props => props.theme.borderRadius.lg};
  border-left: 4px solid ${props => props.theme.colors.primary};
`;

const QuoteText = styled.p`
  font-style: italic;
  color: ${props => props.theme.colors.text.secondary};
  margin-bottom: ${props => props.theme.spacing[2]};
`;

const QuoteReference = styled.p`
  font-size: ${props => props.theme.fontSizes.sm};
  color: ${props => props.theme.colors.text.secondary};
  font-weight: ${props => props.theme.fontWeights.semibold};
`;

const LoginPage = () => {
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const { login } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  
  const from = location.state?.from?.pathname || '/app';

  const {
    register,
    handleSubmit,
    formState: { errors },
    setError
  } = useForm();

  const onSubmit = async (data) => {
    try {
      setLoading(true);
      await login(data.email, data.password);
      navigate(from, { replace: true });
    } catch (error) {
      setError('email', { 
        type: 'manual', 
        message: 'Invalid email or password' 
      });
    } finally {
      setLoading(false);
    }
  };

  const datingQuotes = [
    {
      text: "Marriage is not just spiritual communion and passionate embraces; marriage is also three meals a day, sharing the workload and remembering to carry out the trash.",
      reference: "Dr. Joyce Brothers"
    },
    {
      text: "The goal of marriage is not to think alike, but to think together.",
      reference: "Robert C. Dodds"
    },
    {
      text: "A successful marriage requires falling in love many times, always with the same person.",
      reference: "Mignon McLaughlin"
    }
  ];

  const randomQuote = datingQuotes[Math.floor(Math.random() * datingQuotes.length)];

  return (
    <LoginContainer>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <LoginCard>
          <Logo>LDS Hearts</Logo>
          <Subtitle>Welcome back! Sign in to continue your journey.</Subtitle>
          
          <Form onSubmit={handleSubmit(onSubmit)}>
            <Input
              type="email"
              placeholder="Email address"
              icon={<FiMail />}
              error={errors.email?.message}
              {...register('email', {
                required: 'Email is required',
                pattern: {
                  value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                  message: 'Invalid email address'
                }
              })}
            />
            
            <PasswordInputWrapper>
              <Input
                type={showPassword ? 'text' : 'password'}
                placeholder="Password"
                icon={<FiLock />}
                error={errors.password?.message}
                {...register('password', {
                  required: 'Password is required',
                  minLength: {
                    value: 6,
                    message: 'Password must be at least 6 characters'
                  }
                })}
              />
              <PasswordToggle
                type="button"
                onClick={() => setShowPassword(!showPassword)}
              >
                {showPassword ? <FiEyeOff /> : <FiEye />}
              </PasswordToggle>
            </PasswordInputWrapper>
            
            <ForgotPassword to="/forgot-password">
              Forgot your password?
            </ForgotPassword>
            
            <Button type="submit" loading={loading} fullWidth>
              Sign In
            </Button>
          </Form>
          
          <Divider>
            <span>or</span>
          </Divider>
          
          <SignUpLink>
            Don't have an account? <Link to="/register">Create one here</Link>
          </SignUpLink>
          
          <Quote>
            <QuoteText>"{randomQuote.text}"</QuoteText>
            <QuoteReference>— {randomQuote.reference}</QuoteReference>
          </Quote>
        </LoginCard>
      </motion.div>
    </LoginContainer>
  );
};

export default LoginPage;
