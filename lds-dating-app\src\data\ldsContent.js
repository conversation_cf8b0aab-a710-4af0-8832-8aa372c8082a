// LDS-themed content for the dating app
export const ldsQuotes = [
  {
    text: "And they twain shall be one flesh: so then they are no more twain, but one flesh.",
    reference: "Mark 10:8",
    category: "marriage"
  },
  {
    text: "Whoso findeth a wife findeth a good thing, and obtaineth favour of the Lord.",
    reference: "Proverbs 18:22",
    category: "marriage"
  },
  {
    text: "Marriage is ordained of God unto man. Neither is the man without the woman, neither the woman without the man, in the Lord.",
    reference: "1 Corinthians 11:11",
    category: "marriage"
  },
  {
    text: "The family is ordained of God. Marriage between man and woman is essential to His eternal plan.",
    reference: "The Family: A Proclamation to the World",
    category: "family"
  },
  {
    text: "Husbands, love your wives, even as <PERSON> also loved the church, and gave himself for it.",
    reference: "Ephesians 5:25",
    category: "love"
  },
  {
    text: "And above all things, clothe yourselves with charity, which is the bond of perfectness.",
    reference: "Colossians 3:14",
    category: "love"
  },
  {
    text: "Love is patient, love is kind. It does not envy, it does not boast, it is not proud.",
    reference: "1 Corinthians 13:4",
    category: "love"
  },
  {
    text: "Be ye therefore perfect, even as your Father which is in heaven is perfect.",
    reference: "Matthew 5:48",
    category: "perfection"
  },
  {
    text: "Trust in the Lord with all thine heart; and lean not unto thine own understanding.",
    reference: "Proverbs 3:5",
    category: "faith"
  },
  {
    text: "For I know the plans I have for you, declares the Lord, plans to prosper you and not to harm you, to give you hope and a future.",
    reference: "Jeremiah 29:11",
    category: "hope"
  }
];

export const datingAdviceQuotes = [
  {
    text: "A successful marriage requires falling in love many times, always with the same person.",
    reference: "Mignon McLaughlin",
    category: "marriage"
  },
  {
    text: "The goal of marriage is not to think alike, but to think together.",
    reference: "Robert C. Dodds",
    category: "marriage"
  },
  {
    text: "Marriage is not just spiritual communion and passionate embraces; marriage is also three meals a day, sharing the workload and remembering to carry out the trash.",
    reference: "Dr. Joyce Brothers",
    category: "marriage"
  },
  {
    text: "Being deeply loved by someone gives you strength, while loving someone deeply gives you courage.",
    reference: "Lao Tzu",
    category: "love"
  },
  {
    text: "The best love is the kind that awakens the soul and makes us reach for more, that plants a fire in our hearts and brings peace to our minds.",
    reference: "Nicholas Sparks",
    category: "love"
  }
];

export const ldsProphetsQuotes = [
  {
    text: "Choose your love, love your choice.",
    reference: "President Thomas S. Monson",
    category: "choice"
  },
  {
    text: "The most important single thing you can do to prepare for a temple marriage is to be the kind of person who would attract the kind of person you would want to marry.",
    reference: "Elder Bruce C. Hafen",
    category: "preparation"
  },
  {
    text: "True love is not so much a matter of romance as it is a matter of anxious concern for the well-being of one's companion.",
    reference: "President Gordon B. Hinckley",
    category: "love"
  },
  {
    text: "Marriage is perhaps the most vital of all the decisions and has the most far-reaching effects, for it has to do not only with immediate happiness, but also with eternal joys.",
    reference: "President Spencer W. Kimball",
    category: "marriage"
  },
  {
    text: "The Lord has a plan for each of us. He knows us individually. He knows our needs, our desires, our abilities, and our potential.",
    reference: "Elder David A. Bednar",
    category: "plan"
  }
];

export const templeQuotes = [
  {
    text: "In the temple we can find peace. There we can find answers to our deepest questions and solutions to our most perplexing problems.",
    reference: "President Boyd K. Packer",
    category: "temple"
  },
  {
    text: "The temple is a place of learning. It is a place of peace. It is a house of order.",
    reference: "President Gordon B. Hinckley",
    category: "temple"
  },
  {
    text: "Every temple is a house of learning. There, in a sacred environment, we are taught in the Master's way.",
    reference: "Elder Russell M. Nelson",
    category: "temple"
  }
];

export const dailyInspiration = [
  {
    title: "Faith in Finding Love",
    content: "Trust in the Lord's timing. He knows when you're ready for your eternal companion and when they're ready for you. Focus on becoming the person you want to attract.",
    scripture: "Trust in the Lord with all thine heart; and lean not unto thine own understanding. In all thy ways acknowledge him, and he shall direct thy paths. (Proverbs 3:5-6)"
  },
  {
    title: "Patience in Dating",
    content: "Dating can be challenging, but remember that every experience teaches you something valuable about yourself and what you're looking for in a partner.",
    scripture: "But they that wait upon the Lord shall renew their strength; they shall mount up with wings as eagles. (Isaiah 40:31)"
  },
  {
    title: "Preparing for Marriage",
    content: "The best preparation for marriage is to become the kind of person who would attract the kind of person you want to marry. Work on yourself first.",
    scripture: "And be not conformed to this world: but be ye transformed by the renewing of your mind. (Romans 12:2)"
  },
  {
    title: "Love and Service",
    content: "True love is shown through service. Look for opportunities to serve others, including those you're dating. Service builds character and deepens relationships.",
    scripture: "By this shall all men know that ye are my disciples, if ye have love one to another. (John 13:35)"
  },
  {
    title: "Eternal Perspective",
    content: "Remember that you're not just looking for someone to date, but for your eternal companion. Keep an eternal perspective in your relationships.",
    scripture: "For my thoughts are not your thoughts, neither are your ways my ways, saith the Lord. (Isaiah 55:8)"
  }
];

export const conversationStarters = [
  "What's your favorite scripture and why?",
  "What was the most meaningful part of your mission?",
  "How do you like to spend your Sundays?",
  "What's your favorite temple and what makes it special?",
  "What gospel principle has had the biggest impact on your life?",
  "What are your goals for building an eternal family?",
  "How do you feel the gospel influences your daily decisions?",
  "What's your favorite Church talk or conference address?",
  "How do you like to serve others in your community?",
  "What traditions would you want to start in your future family?",
  "What's your favorite hymn and why does it speak to you?",
  "How has your testimony grown over the years?",
  "What role does prayer play in your daily life?",
  "What's your favorite Book of Mormon story?",
  "How do you prepare for the Sabbath day?"
];

export const getRandomQuote = (category = null) => {
  const allQuotes = [...ldsQuotes, ...datingAdviceQuotes, ...ldsProphetsQuotes, ...templeQuotes];
  
  if (category) {
    const filteredQuotes = allQuotes.filter(quote => quote.category === category);
    return filteredQuotes[Math.floor(Math.random() * filteredQuotes.length)];
  }
  
  return allQuotes[Math.floor(Math.random() * allQuotes.length)];
};

export const getDailyInspiration = () => {
  const today = new Date();
  const dayOfYear = Math.floor((today - new Date(today.getFullYear(), 0, 0)) / 1000 / 60 / 60 / 24);
  return dailyInspiration[dayOfYear % dailyInspiration.length];
};

export const getConversationStarter = () => {
  return conversationStarters[Math.floor(Math.random() * conversationStarters.length)];
};
