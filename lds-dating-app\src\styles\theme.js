const theme = {
  colors: {
    // Instagram-inspired primary colors
    primary: '#E4405F', // Instagram pink/red
    primaryLight: '#F56565',
    primaryDark: '#C53030',
    
    // Secondary colors (Facebook blue)
    secondary: '#1877F2',
    secondaryLight: '#4299E1',
    secondaryDark: '#2B6CB0',
    
    // TikTok accent
    accent: '#FF0050',
    accentLight: '#FF6B9D',
    
    // Neutral colors (Instagram style)
    white: '#FFFFFF',
    black: '#000000',
    gray: {
      50: '#FAFAFA',
      100: '#F4F4F4',
      200: '#EFEFEF',
      300: '#DBDBDB',
      400: '#C7C7C7',
      500: '#8E8E8E',
      600: '#737373',
      700: '#262626',
      800: '#1C1C1C',
      900: '#000000'
    },
    
    // Status colors
    success: '#00D4AA',
    warning: '#FFB800',
    error: '#ED4956',
    info: '#0095F6',
    
    // Background colors
    background: '#FAFAFA',
    surface: '#FFFFFF',
    overlay: 'rgba(0, 0, 0, 0.65)',
    
    // Text colors
    text: {
      primary: '#262626',
      secondary: '#8E8E8E',
      tertiary: '#C7C7C7',
      inverse: '#FFFFFF'
    },
    
    // Border colors
    border: {
      light: '#EFEFEF',
      medium: '#DBDBDB',
      dark: '#C7C7C7'
    }
  },
  
  fonts: {
    primary: "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif",
    secondary: "'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif"
  },
  
  fontSizes: {
    xs: '0.75rem',    // 12px
    sm: '0.875rem',   // 14px
    base: '1rem',     // 16px
    lg: '1.125rem',   // 18px
    xl: '1.25rem',    // 20px
    '2xl': '1.5rem',  // 24px
    '3xl': '1.875rem', // 30px
    '4xl': '2.25rem', // 36px
    '5xl': '3rem',    // 48px
  },
  
  fontWeights: {
    light: 300,
    normal: 400,
    medium: 500,
    semibold: 600,
    bold: 700
  },
  
  spacing: {
    0: '0',
    1: '0.25rem',   // 4px
    2: '0.5rem',    // 8px
    3: '0.75rem',   // 12px
    4: '1rem',      // 16px
    5: '1.25rem',   // 20px
    6: '1.5rem',    // 24px
    8: '2rem',      // 32px
    10: '2.5rem',   // 40px
    12: '3rem',     // 48px
    16: '4rem',     // 64px
    20: '5rem',     // 80px
    24: '6rem',     // 96px
  },
  
  borderRadius: {
    none: '0',
    sm: '0.125rem',   // 2px
    base: '0.25rem',  // 4px
    md: '0.375rem',   // 6px
    lg: '0.5rem',     // 8px
    xl: '0.75rem',    // 12px
    '2xl': '1rem',    // 16px
    '3xl': '1.5rem',  // 24px
    full: '9999px'
  },
  
  shadows: {
    sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
    base: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
    md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
    lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
    xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
  },
  
  breakpoints: {
    sm: '640px',
    md: '768px',
    lg: '1024px',
    xl: '1280px',
    '2xl': '1536px'
  },
  
  zIndex: {
    dropdown: 1000,
    sticky: 1020,
    fixed: 1030,
    modal: 1040,
    popover: 1050,
    tooltip: 1060
  }
};

export default theme;
