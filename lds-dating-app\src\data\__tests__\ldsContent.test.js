import { 
  getRandomQuote, 
  getDailyInspiration, 
  getConversationStarter,
  ldsQuotes,
  datingAdviceQuotes,
  dailyInspiration,
  conversationStarters
} from '../ldsContent';

describe('LDS Content', () => {
  describe('getRandomQuote', () => {
    test('returns a quote object', () => {
      const quote = getRandomQuote();
      expect(quote).toHaveProperty('text');
      expect(quote).toHaveProperty('reference');
      expect(quote).toHaveProperty('category');
    });

    test('returns quote from specific category', () => {
      const marriageQuote = getRandomQuote('marriage');
      expect(marriageQuote.category).toBe('marriage');
    });

    test('returns different quotes on multiple calls', () => {
      const quotes = new Set();
      for (let i = 0; i < 10; i++) {
        quotes.add(getRandomQuote().text);
      }
      // Should have some variety (not all the same)
      expect(quotes.size).toBeGreaterThan(1);
    });
  });

  describe('getDailyInspiration', () => {
    test('returns inspiration object', () => {
      const inspiration = getDailyInspiration();
      expect(inspiration).toHaveProperty('title');
      expect(inspiration).toHaveProperty('content');
      expect(inspiration).toHaveProperty('scripture');
    });

    test('returns consistent inspiration for same day', () => {
      const inspiration1 = getDailyInspiration();
      const inspiration2 = getDailyInspiration();
      expect(inspiration1).toEqual(inspiration2);
    });
  });

  describe('getConversationStarter', () => {
    test('returns a string', () => {
      const starter = getConversationStarter();
      expect(typeof starter).toBe('string');
      expect(starter.length).toBeGreaterThan(0);
    });

    test('returns different starters on multiple calls', () => {
      const starters = new Set();
      for (let i = 0; i < 10; i++) {
        starters.add(getConversationStarter());
      }
      expect(starters.size).toBeGreaterThan(1);
    });
  });

  describe('Data integrity', () => {
    test('all LDS quotes have required properties', () => {
      ldsQuotes.forEach(quote => {
        expect(quote).toHaveProperty('text');
        expect(quote).toHaveProperty('reference');
        expect(quote).toHaveProperty('category');
        expect(typeof quote.text).toBe('string');
        expect(typeof quote.reference).toBe('string');
        expect(typeof quote.category).toBe('string');
      });
    });

    test('all dating advice quotes have required properties', () => {
      datingAdviceQuotes.forEach(quote => {
        expect(quote).toHaveProperty('text');
        expect(quote).toHaveProperty('reference');
        expect(quote).toHaveProperty('category');
      });
    });

    test('all daily inspirations have required properties', () => {
      dailyInspiration.forEach(inspiration => {
        expect(inspiration).toHaveProperty('title');
        expect(inspiration).toHaveProperty('content');
        expect(inspiration).toHaveProperty('scripture');
      });
    });

    test('conversation starters are all strings', () => {
      conversationStarters.forEach(starter => {
        expect(typeof starter).toBe('string');
        expect(starter.length).toBeGreaterThan(0);
      });
    });
  });
});
