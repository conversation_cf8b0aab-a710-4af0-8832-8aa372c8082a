import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import { FiUser, FiMapPin, FiHeart, FiCalendar, FiCamera } from 'react-icons/fi';
import { useAuth } from '../contexts/AuthContext';
import Button from '../components/UI/Button';
import Input from '../components/UI/Input';
import Card from '../components/UI/Card';

const SetupContainer = styled.div`
  min-height: 100vh;
  background: ${props => props.theme.colors.background};
  padding: ${props => props.theme.spacing[4]};
`;

const SetupContent = styled.div`
  max-width: 800px;
  margin: 0 auto;
  padding: ${props => props.theme.spacing[8]} 0;
`;

const Header = styled.div`
  text-align: center;
  margin-bottom: ${props => props.theme.spacing[8]};
`;

const Title = styled.h1`
  font-size: ${props => props.theme.fontSizes['3xl']};
  font-weight: ${props => props.theme.fontWeights.bold};
  color: ${props => props.theme.colors.text.primary};
  margin-bottom: ${props => props.theme.spacing[4]};
`;

const Subtitle = styled.p`
  font-size: ${props => props.theme.fontSizes.lg};
  color: ${props => props.theme.colors.text.secondary};
  max-width: 600px;
  margin: 0 auto;
`;

const ProgressBar = styled.div`
  width: 100%;
  height: 8px;
  background: ${props => props.theme.colors.gray[200]};
  border-radius: ${props => props.theme.borderRadius.full};
  margin-bottom: ${props => props.theme.spacing[8]};
  overflow: hidden;
`;

const ProgressFill = styled.div`
  height: 100%;
  background: linear-gradient(45deg, ${props => props.theme.colors.primary}, ${props => props.theme.colors.accent});
  border-radius: ${props => props.theme.borderRadius.full};
  transition: width 0.3s ease;
  width: ${props => props.progress}%;
`;

const StepCard = styled(Card)`
  padding: ${props => props.theme.spacing[8]};
  margin-bottom: ${props => props.theme.spacing[6]};
`;

const StepHeader = styled.div`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing[3]};
  margin-bottom: ${props => props.theme.spacing[6]};
`;

const StepIcon = styled.div`
  width: 48px;
  height: 48px;
  background: linear-gradient(45deg, ${props => props.theme.colors.primary}, ${props => props.theme.colors.accent});
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: ${props => props.theme.colors.white};
  font-size: ${props => props.theme.fontSizes.xl};
`;

const StepTitle = styled.h2`
  font-size: ${props => props.theme.fontSizes.xl};
  font-weight: ${props => props.theme.fontWeights.semibold};
  color: ${props => props.theme.colors.text.primary};
  margin: 0;
`;

const StepDescription = styled.p`
  color: ${props => props.theme.colors.text.secondary};
  margin: 0;
`;

const Form = styled.form`
  display: flex;
  flex-direction: column;
  gap: ${props => props.theme.spacing[4]};
`;

const FormRow = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: ${props => props.theme.spacing[4]};
`;

const Select = styled.select`
  width: 100%;
  padding: ${props => props.theme.spacing[3]} ${props => props.theme.spacing[4]};
  font-size: ${props => props.theme.fontSizes.base};
  font-family: ${props => props.theme.fonts.primary};
  background: ${props => props.theme.colors.white};
  border: 1px solid ${props => props.theme.colors.border.medium};
  border-radius: ${props => props.theme.borderRadius.lg};
  color: ${props => props.theme.colors.text.primary};
  
  &:focus {
    outline: none;
    border-color: ${props => props.theme.colors.primary};
    box-shadow: 0 0 0 3px ${props => props.theme.colors.primary}20;
  }
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: ${props => props.theme.spacing[4]};
  justify-content: flex-end;
  margin-top: ${props => props.theme.spacing[6]};
`;

const ProfileSetupPage = () => {
  const [currentStep, setCurrentStep] = useState(1);
  const [loading, setLoading] = useState(false);
  const { updateUserProfile } = useAuth();
  const navigate = useNavigate();

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
    setValue
  } = useForm();

  const totalSteps = 4;
  const progress = (currentStep / totalSteps) * 100;

  const onSubmit = async (data) => {
    try {
      setLoading(true);
      
      // Prepare profile data
      const profileData = {
        ...data,
        profileComplete: true,
        age: parseInt(data.age),
        isReturnedMissionary: data.isReturnedMissionary === 'yes',
        photos: [], // Will be handled separately
        interests: data.interests || []
      };

      await updateUserProfile(profileData);
      navigate('/app');
    } catch (error) {
      console.error('Profile setup error:', error);
    } finally {
      setLoading(false);
    }
  };

  const nextStep = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <StepCard>
            <StepHeader>
              <StepIcon><FiUser /></StepIcon>
              <div>
                <StepTitle>Basic Information</StepTitle>
                <StepDescription>Tell us about yourself</StepDescription>
              </div>
            </StepHeader>
            
            <Form>
              <FormRow>
                <Input
                  type="number"
                  label="Age"
                  placeholder="25"
                  error={errors.age?.message}
                  {...register('age', {
                    required: 'Age is required',
                    min: { value: 18, message: 'Must be at least 18 years old' },
                    max: { value: 100, message: 'Please enter a valid age' }
                  })}
                />
                <div>
                  <label>Gender</label>
                  <Select {...register('gender', { required: 'Gender is required' })}>
                    <option value="">Select Gender</option>
                    <option value="male">Male</option>
                    <option value="female">Female</option>
                  </Select>
                </div>
              </FormRow>
              
              <Input
                multiline
                rows={4}
                label="Bio"
                placeholder="Tell others about yourself, your interests, and what makes you unique..."
                error={errors.bio?.message}
                {...register('bio', {
                  required: 'Bio is required',
                  minLength: { value: 50, message: 'Bio must be at least 50 characters' },
                  maxLength: { value: 500, message: 'Bio must be less than 500 characters' }
                })}
              />
            </Form>
          </StepCard>
        );
        
      case 2:
        return (
          <StepCard>
            <StepHeader>
              <StepIcon><FiMapPin /></StepIcon>
              <div>
                <StepTitle>Location & Church Information</StepTitle>
                <StepDescription>Help others find you in their area</StepDescription>
              </div>
            </StepHeader>
            
            <Form>
              <FormRow>
                <Input
                  label="City"
                  placeholder="Salt Lake City"
                  error={errors.city?.message}
                  {...register('city', { required: 'City is required' })}
                />
                <Input
                  label="Country"
                  placeholder="United States"
                  error={errors.country?.message}
                  {...register('country', { required: 'Country is required' })}
                />
              </FormRow>
              
              <FormRow>
                <Input
                  label="Ward"
                  placeholder="University 3rd Ward"
                  error={errors.ward?.message}
                  {...register('ward', { required: 'Ward is required' })}
                />
                <Input
                  label="Stake"
                  placeholder="University Stake"
                  error={errors.stake?.message}
                  {...register('stake', { required: 'Stake is required' })}
                />
              </FormRow>
              
              <div>
                <label>Activity Level</label>
                <Select {...register('activityLevel', { required: 'Activity level is required' })}>
                  <option value="">Select Activity Level</option>
                  <option value="very-active">Very Active</option>
                  <option value="active">Active</option>
                  <option value="somewhat-active">Somewhat Active</option>
                  <option value="less-active">Less Active</option>
                </Select>
              </div>
              
              <Input
                label="Member Number (Optional)"
                placeholder="123-4567-8901"
                helperText="This helps verify your membership but is optional"
                {...register('memberNumber')}
              />
            </Form>
          </StepCard>
        );
        
      case 3:
        return (
          <StepCard>
            <StepHeader>
              <StepIcon><FiHeart /></StepIcon>
              <div>
                <StepTitle>Mission & Relationship Goals</StepTitle>
                <StepDescription>Share your mission experience and what you're looking for</StepDescription>
              </div>
            </StepHeader>

            <Form>
              <div>
                <label>Are you a returned missionary?</label>
                <Select {...register('isReturnedMissionary', { required: 'Please select an option' })}>
                  <option value="">Select an option</option>
                  <option value="yes">Yes</option>
                  <option value="no">No</option>
                  <option value="currently-serving">Currently Serving</option>
                  <option value="planning-to-serve">Planning to Serve</option>
                </Select>
              </div>

              {watch('isReturnedMissionary') === 'yes' && (
                <FormRow>
                  <Input
                    label="Mission Location"
                    placeholder="Brazil São Paulo South Mission"
                    error={errors.missionLocation?.message}
                    {...register('missionLocation', {
                      required: watch('isReturnedMissionary') === 'yes' ? 'Mission location is required' : false
                    })}
                  />
                  <Input
                    label="Mission Years"
                    placeholder="2018-2020"
                    error={errors.missionYears?.message}
                    {...register('missionYears', {
                      required: watch('isReturnedMissionary') === 'yes' ? 'Mission years are required' : false
                    })}
                  />
                </FormRow>
              )}

              <Input
                multiline
                rows={4}
                label="What are you looking for in a relationship?"
                placeholder="I'm looking for someone who shares my faith and values, wants to build an eternal family together, and enjoys outdoor activities..."
                error={errors.lookingFor?.message}
                {...register('lookingFor', {
                  required: 'Please describe what you\'re looking for',
                  minLength: { value: 30, message: 'Please provide more detail (at least 30 characters)' }
                })}
              />
            </Form>
          </StepCard>
        );

      case 4:
        return (
          <StepCard>
            <StepHeader>
              <StepIcon><FiCamera /></StepIcon>
              <div>
                <StepTitle>Photos & Final Details</StepTitle>
                <StepDescription>Add photos to complete your profile</StepDescription>
              </div>
            </StepHeader>

            <Form>
              <div>
                <label>Upload Photos (At least 2 required)</label>
                <p style={{ fontSize: '14px', color: '#8E8E8E', marginBottom: '16px' }}>
                  Add photos that show your personality. First photo will be your main profile picture.
                </p>
                {/* Photo upload functionality would go here */}
                <div style={{
                  border: '2px dashed #DBDBDB',
                  borderRadius: '12px',
                  padding: '40px',
                  textAlign: 'center',
                  background: '#FAFAFA'
                }}>
                  <FiCamera style={{ fontSize: '48px', color: '#8E8E8E', marginBottom: '16px' }} />
                  <p style={{ color: '#8E8E8E' }}>Photo upload functionality will be implemented here</p>
                </div>
              </div>

              <div>
                <label>Interests (Select all that apply)</label>
                <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', gap: '12px', marginTop: '12px' }}>
                  {[
                    'Temple Worship', 'Family History', 'Outdoor Activities', 'Reading', 'Music',
                    'Sports', 'Cooking', 'Travel', 'Art', 'Dancing', 'Fitness', 'Movies',
                    'Board Games', 'Hiking', 'Photography', 'Volunteering'
                  ].map((interest) => (
                    <label key={interest} style={{ display: 'flex', alignItems: 'center', gap: '8px', cursor: 'pointer' }}>
                      <input
                        type="checkbox"
                        value={interest}
                        {...register('interests')}
                        style={{ accentColor: '#E4405F' }}
                      />
                      {interest}
                    </label>
                  ))}
                </div>
              </div>
            </Form>
          </StepCard>
        );

      default:
        return null;
    }
  };

  return (
    <SetupContainer>
      <SetupContent>
        <Header>
          <Title>Complete Your Profile</Title>
          <Subtitle>
            Help us create the perfect profile to connect you with your ideal match. 
            This information helps other members get to know you better.
          </Subtitle>
        </Header>
        
        <ProgressBar>
          <ProgressFill progress={progress} />
        </ProgressBar>
        
        <motion.div
          key={currentStep}
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          exit={{ opacity: 0, x: -20 }}
          transition={{ duration: 0.3 }}
        >
          {renderStep()}
        </motion.div>
        
        <ButtonGroup>
          {currentStep > 1 && (
            <Button variant="outline" onClick={prevStep}>
              Previous
            </Button>
          )}
          
          {currentStep < totalSteps ? (
            <Button onClick={nextStep}>
              Next Step
            </Button>
          ) : (
            <Button onClick={handleSubmit(onSubmit)} loading={loading}>
              Complete Profile
            </Button>
          )}
        </ButtonGroup>
      </SetupContent>
    </SetupContainer>
  );
};

export default ProfileSetupPage;
