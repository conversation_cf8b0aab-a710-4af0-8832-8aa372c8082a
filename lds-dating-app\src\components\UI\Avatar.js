import React from 'react';
import styled, { css } from 'styled-components';

const AvatarWrapper = styled.div`
  position: relative;
  display: inline-block;
`;

const AvatarImage = styled.img`
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
  border: 2px solid ${props => props.theme.colors.white};
  box-shadow: ${props => props.theme.shadows.sm};
`;

const AvatarContainer = styled.div`
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: ${props => props.theme.colors.gray[200]};
  color: ${props => props.theme.colors.text.secondary};
  font-weight: ${props => props.theme.fontWeights.semibold};
  overflow: hidden;

  ${props => props.size === 'xs' && css`
    width: 24px;
    height: 24px;
    font-size: ${props.theme.fontSizes.xs};
  `}

  ${props => props.size === 'sm' && css`
    width: 32px;
    height: 32px;
    font-size: ${props.theme.fontSizes.sm};
  `}

  ${props => props.size === 'md' && css`
    width: 40px;
    height: 40px;
    font-size: ${props.theme.fontSizes.base};
  `}

  ${props => props.size === 'lg' && css`
    width: 56px;
    height: 56px;
    font-size: ${props.theme.fontSizes.lg};
  `}

  ${props => props.size === 'xl' && css`
    width: 80px;
    height: 80px;
    font-size: ${props.theme.fontSizes.xl};
  `}

  ${props => props.size === '2xl' && css`
    width: 120px;
    height: 120px;
    font-size: ${props.theme.fontSizes['2xl']};
  `}

  ${props => props.online && css`
    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      right: 0;
      width: 25%;
      height: 25%;
      background: ${props.theme.colors.success};
      border: 2px solid ${props.theme.colors.white};
      border-radius: 50%;
    }
  `}

  ${props => props.story && css`
    background: linear-gradient(45deg, ${props.theme.colors.primary}, ${props.theme.colors.accent});
    padding: 2px;
    
    &::before {
      content: '';
      position: absolute;
      inset: 2px;
      background: ${props.theme.colors.white};
      border-radius: 50%;
      z-index: 1;
    }
    
    ${AvatarImage} {
      position: relative;
      z-index: 2;
      border: none;
    }
  `}
`;

const Badge = styled.div`
  position: absolute;
  top: -4px;
  right: -4px;
  background: ${props => props.theme.colors.primary};
  color: ${props => props.theme.colors.white};
  border-radius: 50%;
  min-width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: ${props => props.theme.fontSizes.xs};
  font-weight: ${props => props.theme.fontWeights.bold};
  border: 2px solid ${props => props.theme.colors.white};
`;

const getInitials = (name) => {
  if (!name) return '?';
  return name
    .split(' ')
    .map(word => word.charAt(0))
    .join('')
    .toUpperCase()
    .slice(0, 2);
};

const Avatar = ({ 
  src, 
  alt, 
  name, 
  size = 'md', 
  online = false, 
  story = false,
  badge,
  onClick,
  ...props 
}) => {
  return (
    <AvatarWrapper onClick={onClick} {...props}>
      <AvatarContainer size={size} online={online} story={story}>
        {src ? (
          <AvatarImage src={src} alt={alt || name} />
        ) : (
          getInitials(name)
        )}
      </AvatarContainer>
      {badge && <Badge>{badge}</Badge>}
    </AvatarWrapper>
  );
};

export default Avatar;
