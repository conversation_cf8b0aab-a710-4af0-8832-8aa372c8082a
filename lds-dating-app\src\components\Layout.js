import React from 'react';
import { Outlet, useLocation, useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { FiHome, FiHeart, FiMessageCircle, FiUser, FiSettings } from 'react-icons/fi';
import { useAuth } from '../contexts/AuthContext';
import Avatar from './UI/Avatar';

const LayoutContainer = styled.div`
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background: ${props => props.theme.colors.background};
`;

const Header = styled.header`
  background: ${props => props.theme.colors.white};
  border-bottom: 1px solid ${props => props.theme.colors.border.light};
  padding: ${props => props.theme.spacing[4]} 0;
  position: sticky;
  top: 0;
  z-index: ${props => props.theme.zIndex.sticky};
`;

const HeaderContent = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 ${props => props.theme.spacing[4]};
  display: flex;
  align-items: center;
  justify-content: space-between;
`;

const Logo = styled.h1`
  font-size: ${props => props.theme.fontSizes['2xl']};
  font-weight: ${props => props.theme.fontWeights.bold};
  background: linear-gradient(45deg, ${props => props.theme.colors.primary}, ${props => props.theme.colors.accent});
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0;
`;

const UserInfo = styled.div`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing[3]};
`;

const UserName = styled.span`
  font-weight: ${props => props.theme.fontWeights.medium};
  color: ${props => props.theme.colors.text.primary};
`;

const Main = styled.main`
  flex: 1;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  padding: ${props => props.theme.spacing[6]} ${props => props.theme.spacing[4]};
`;

const BottomNav = styled.nav`
  background: ${props => props.theme.colors.white};
  border-top: 1px solid ${props => props.theme.colors.border.light};
  padding: ${props => props.theme.spacing[3]} 0;
  position: sticky;
  bottom: 0;
  z-index: ${props => props.theme.zIndex.sticky};

  @media (min-width: ${props => props.theme.breakpoints.md}) {
    display: none;
  }
`;

const NavList = styled.ul`
  display: flex;
  justify-content: space-around;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 ${props => props.theme.spacing[4]};
`;

const NavItem = styled.li`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: ${props => props.theme.spacing[1]};
`;

const NavButton = styled.button`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: ${props => props.theme.spacing[1]};
  padding: ${props => props.theme.spacing[2]};
  border-radius: ${props => props.theme.borderRadius.lg};
  transition: all 0.2s ease-in-out;
  color: ${props => props.active ? props.theme.colors.primary : props.theme.colors.text.secondary};
  background: ${props => props.active ? `${props.theme.colors.primary}10` : 'transparent'};

  &:hover {
    background: ${props => props.theme.colors.gray[100]};
  }

  svg {
    font-size: ${props => props.theme.fontSizes.xl};
  }
`;

const NavLabel = styled.span`
  font-size: ${props => props.theme.fontSizes.xs};
  font-weight: ${props => props.theme.fontWeights.medium};
`;

const SideNav = styled.nav`
  display: none;
  
  @media (min-width: ${props => props.theme.breakpoints.md}) {
    display: block;
    position: fixed;
    left: 0;
    top: 80px;
    bottom: 0;
    width: 250px;
    background: ${props => props.theme.colors.white};
    border-right: 1px solid ${props => props.theme.colors.border.light};
    padding: ${props => props.theme.spacing[6]} 0;
  }
`;

const SideNavList = styled.ul`
  display: flex;
  flex-direction: column;
  gap: ${props => props.theme.spacing[2]};
  padding: 0 ${props => props.theme.spacing[4]};
`;

const SideNavItem = styled.li``;

const SideNavButton = styled.button`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing[3]};
  width: 100%;
  padding: ${props => props.theme.spacing[3]} ${props => props.theme.spacing[4]};
  border-radius: ${props => props.theme.borderRadius.lg};
  transition: all 0.2s ease-in-out;
  color: ${props => props.active ? props.theme.colors.primary : props.theme.colors.text.primary};
  background: ${props => props.active ? `${props.theme.colors.primary}10` : 'transparent'};
  font-weight: ${props => props.active ? props.theme.fontWeights.semibold : props.theme.fontWeights.normal};

  &:hover {
    background: ${props => props.theme.colors.gray[100]};
  }

  svg {
    font-size: ${props => props.theme.fontSizes.lg};
  }
`;

const ContentWrapper = styled.div`
  @media (min-width: ${props => props.theme.breakpoints.md}) {
    margin-left: 250px;
  }
`;

const Layout = () => {
  const { userProfile } = useAuth();
  const location = useLocation();
  const navigate = useNavigate();

  const navItems = [
    { path: '/app', icon: FiHome, label: 'Discover' },
    { path: '/app/matches', icon: FiHeart, label: 'Matches' },
    { path: '/app/messages', icon: FiMessageCircle, label: 'Messages' },
    { path: '/app/profile', icon: FiUser, label: 'Profile' },
    { path: '/app/settings', icon: FiSettings, label: 'Settings' }
  ];

  const isActive = (path) => {
    if (path === '/app') {
      return location.pathname === '/app';
    }
    return location.pathname.startsWith(path);
  };

  return (
    <LayoutContainer>
      <Header>
        <HeaderContent>
          <Logo>LDS Hearts</Logo>
          <UserInfo>
            <UserName>{userProfile?.displayName}</UserName>
            <Avatar 
              src={userProfile?.photos?.[0]} 
              name={userProfile?.displayName}
              size="sm"
            />
          </UserInfo>
        </HeaderContent>
      </Header>

      <SideNav>
        <SideNavList>
          {navItems.map((item) => (
            <SideNavItem key={item.path}>
              <SideNavButton
                active={isActive(item.path)}
                onClick={() => navigate(item.path)}
              >
                <item.icon />
                {item.label}
              </SideNavButton>
            </SideNavItem>
          ))}
        </SideNavList>
      </SideNav>

      <ContentWrapper>
        <Main>
          <Outlet />
        </Main>
      </ContentWrapper>

      <BottomNav>
        <NavList>
          {navItems.map((item) => (
            <NavItem key={item.path}>
              <NavButton
                active={isActive(item.path)}
                onClick={() => navigate(item.path)}
              >
                <item.icon />
                <NavLabel>{item.label}</NavLabel>
              </NavButton>
            </NavItem>
          ))}
        </NavList>
      </BottomNav>
    </LayoutContainer>
  );
};

export default Layout;
