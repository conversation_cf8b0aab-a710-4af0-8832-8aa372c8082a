import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { FiMessageCircle, FiSend } from 'react-icons/fi';
import { useMatch } from '../contexts/MatchContext';
import { useAuth } from '../contexts/AuthContext';
import Card from '../components/UI/Card';
import Avatar from '../components/UI/Avatar';
import Button from '../components/UI/Button';
import Input from '../components/UI/Input';

const MessagesContainer = styled.div`
  max-width: 1000px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: 350px 1fr;
  gap: ${props => props.theme.spacing[6]};
  height: calc(100vh - 200px);

  @media (max-width: ${props => props.theme.breakpoints.md}) {
    grid-template-columns: 1fr;
    height: auto;
  }
`;

const ConversationsList = styled(Card)`
  padding: 0;
  overflow-y: auto;
`;

const ConversationsHeader = styled.div`
  padding: ${props => props.theme.spacing[6]};
  border-bottom: 1px solid ${props => props.theme.colors.border.light};
`;

const ConversationsTitle = styled.h2`
  font-size: ${props => props.theme.fontSizes.xl};
  font-weight: ${props => props.theme.fontWeights.semibold};
  color: ${props => props.theme.colors.text.primary};
  margin: 0;
`;

const ConversationItem = styled.div`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing[3]};
  padding: ${props => props.theme.spacing[4]} ${props => props.theme.spacing[6]};
  cursor: pointer;
  transition: background-color 0.2s ease;
  border-bottom: 1px solid ${props => props.theme.colors.border.light};
  
  &:hover {
    background: ${props => props.theme.colors.gray[50]};
  }
  
  ${props => props.active && `
    background: ${props.theme.colors.primary}10;
    border-right: 3px solid ${props.theme.colors.primary};
  `}
`;

const ConversationInfo = styled.div`
  flex: 1;
  min-width: 0;
`;

const ConversationName = styled.div`
  font-weight: ${props => props.theme.fontWeights.semibold};
  color: ${props => props.theme.colors.text.primary};
  margin-bottom: ${props => props.theme.spacing[1]};
`;

const LastMessage = styled.div`
  font-size: ${props => props.theme.fontSizes.sm};
  color: ${props => props.theme.colors.text.secondary};
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
`;

const ChatArea = styled(Card)`
  display: flex;
  flex-direction: column;
  padding: 0;
  height: 100%;
`;

const ChatHeader = styled.div`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing[3]};
  padding: ${props => props.theme.spacing[6]};
  border-bottom: 1px solid ${props => props.theme.colors.border.light};
`;

const ChatHeaderInfo = styled.div`
  flex: 1;
`;

const ChatHeaderName = styled.h3`
  font-size: ${props => props.theme.fontSizes.lg};
  font-weight: ${props => props.theme.fontWeights.semibold};
  color: ${props => props.theme.colors.text.primary};
  margin: 0;
`;

const MessagesArea = styled.div`
  flex: 1;
  padding: ${props => props.theme.spacing[6]};
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: ${props => props.theme.spacing[3]};
`;

const Message = styled.div`
  max-width: 70%;
  padding: ${props => props.theme.spacing[3]} ${props => props.theme.spacing[4]};
  border-radius: ${props => props.theme.borderRadius.xl};
  
  ${props => props.own ? `
    align-self: flex-end;
    background: linear-gradient(45deg, ${props.theme.colors.primary}, ${props.theme.colors.accent});
    color: ${props.theme.colors.white};
  ` : `
    align-self: flex-start;
    background: ${props.theme.colors.gray[100]};
    color: ${props.theme.colors.text.primary};
  `}
`;

const MessageInput = styled.div`
  display: flex;
  gap: ${props => props.theme.spacing[3]};
  padding: ${props => props.theme.spacing[6]};
  border-top: 1px solid ${props => props.theme.colors.border.light};
`;

const EmptyState = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
  padding: ${props => props.theme.spacing[8]};
`;

const EmptyStateIcon = styled.div`
  font-size: ${props => props.theme.fontSizes['4xl']};
  color: ${props => props.theme.colors.text.secondary};
  margin-bottom: ${props => props.theme.spacing[4]};
`;

const EmptyStateTitle = styled.h3`
  font-size: ${props => props.theme.fontSizes.xl};
  font-weight: ${props => props.theme.fontWeights.semibold};
  color: ${props => props.theme.colors.text.primary};
  margin-bottom: ${props => props.theme.spacing[2]};
`;

const EmptyStateText = styled.p`
  color: ${props => props.theme.colors.text.secondary};
  line-height: 1.6;
`;

const MessagesPage = () => {
  const [conversations, setConversations] = useState([]);
  const [selectedConversation, setSelectedConversation] = useState(null);
  const [messages, setMessages] = useState([]);
  const [newMessage, setNewMessage] = useState('');
  const [loading, setLoading] = useState(true);
  const { matches } = useMatch();
  const { userProfile } = useAuth();

  useEffect(() => {
    // Convert matches to conversations
    setConversations(matches);
    setLoading(false);
  }, [matches]);

  const handleSendMessage = () => {
    if (newMessage.trim() && selectedConversation) {
      // Add message sending logic here
      const message = {
        id: Date.now(),
        text: newMessage,
        senderId: userProfile.uid,
        timestamp: new Date(),
        own: true
      };
      
      setMessages(prev => [...prev, message]);
      setNewMessage('');
    }
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  if (loading) {
    return (
      <MessagesContainer>
        <div>Loading conversations...</div>
      </MessagesContainer>
    );
  }

  return (
    <MessagesContainer>
      <ConversationsList>
        <ConversationsHeader>
          <ConversationsTitle>Messages</ConversationsTitle>
        </ConversationsHeader>
        
        {conversations.length > 0 ? (
          conversations.map((conversation) => {
            const otherUser = conversation.otherUser;
            return (
              <ConversationItem
                key={conversation.id}
                active={selectedConversation?.id === conversation.id}
                onClick={() => setSelectedConversation(conversation)}
              >
                <Avatar
                  src={otherUser?.photos?.[0]}
                  name={otherUser?.displayName}
                  size="md"
                />
                <ConversationInfo>
                  <ConversationName>{otherUser?.displayName}</ConversationName>
                  <LastMessage>
                    {conversation.lastMessage || 'Start a conversation...'}
                  </LastMessage>
                </ConversationInfo>
              </ConversationItem>
            );
          })
        ) : (
          <EmptyState>
            <EmptyStateIcon>💬</EmptyStateIcon>
            <EmptyStateTitle>No Conversations</EmptyStateTitle>
            <EmptyStateText>
              When you match with someone, you can start chatting here.
            </EmptyStateText>
          </EmptyState>
        )}
      </ConversationsList>

      <ChatArea>
        {selectedConversation ? (
          <>
            <ChatHeader>
              <Avatar
                src={selectedConversation.otherUser?.photos?.[0]}
                name={selectedConversation.otherUser?.displayName}
                size="md"
              />
              <ChatHeaderInfo>
                <ChatHeaderName>
                  {selectedConversation.otherUser?.displayName}
                </ChatHeaderName>
              </ChatHeaderInfo>
            </ChatHeader>

            <MessagesArea>
              {messages.length > 0 ? (
                messages.map((message) => (
                  <Message key={message.id} own={message.own}>
                    {message.text}
                  </Message>
                ))
              ) : (
                <EmptyState>
                  <EmptyStateIcon>👋</EmptyStateIcon>
                  <EmptyStateTitle>Say Hello!</EmptyStateTitle>
                  <EmptyStateText>
                    Start the conversation with {selectedConversation.otherUser?.displayName}
                  </EmptyStateText>
                </EmptyState>
              )}
            </MessagesArea>

            <MessageInput>
              <Input
                placeholder="Type a message..."
                value={newMessage}
                onChange={(e) => setNewMessage(e.target.value)}
                onKeyPress={handleKeyPress}
              />
              <Button onClick={handleSendMessage}>
                <FiSend />
              </Button>
            </MessageInput>
          </>
        ) : (
          <EmptyState>
            <EmptyStateIcon>💬</EmptyStateIcon>
            <EmptyStateTitle>Select a Conversation</EmptyStateTitle>
            <EmptyStateText>
              Choose a conversation from the left to start chatting.
            </EmptyStateText>
          </EmptyState>
        )}
      </ChatArea>
    </MessagesContainer>
  );
};

export default MessagesPage;
