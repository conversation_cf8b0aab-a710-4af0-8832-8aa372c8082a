import React, { useState } from 'react';
import styled from 'styled-components';
import { FiUser, FiSettings, FiShield, FiLogOut, FiHeart } from 'react-icons/fi';
import { useAuth } from '../contexts/AuthContext';
import Button from '../components/UI/Button';
import Card from '../components/UI/Card';
import Input from '../components/UI/Input';
import Modal from '../components/UI/Modal';

const SettingsContainer = styled.div`
  max-width: 800px;
  margin: 0 auto;
`;

const Header = styled.div`
  text-align: center;
  margin-bottom: ${props => props.theme.spacing[8]};
`;

const Title = styled.h1`
  font-size: ${props => props.theme.fontSizes['2xl']};
  font-weight: ${props => props.theme.fontWeights.bold};
  color: ${props => props.theme.colors.text.primary};
  margin-bottom: ${props => props.theme.spacing[2]};
`;

const SettingsSection = styled(Card)`
  padding: ${props => props.theme.spacing[6]};
  margin-bottom: ${props => props.theme.spacing[6]};
`;

const SectionTitle = styled.h2`
  font-size: ${props => props.theme.fontSizes.xl};
  font-weight: ${props => props.theme.fontWeights.semibold};
  color: ${props => props.theme.colors.text.primary};
  margin-bottom: ${props => props.theme.spacing[4]};
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing[2]};
`;

const SettingItem = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: ${props => props.theme.spacing[4]} 0;
  border-bottom: 1px solid ${props => props.theme.colors.border.light};
  
  &:last-child {
    border-bottom: none;
  }
`;

const SettingInfo = styled.div`
  flex: 1;
`;

const SettingLabel = styled.div`
  font-weight: ${props => props.theme.fontWeights.medium};
  color: ${props => props.theme.colors.text.primary};
  margin-bottom: ${props => props.theme.spacing[1]};
`;

const SettingDescription = styled.div`
  font-size: ${props => props.theme.fontSizes.sm};
  color: ${props => props.theme.colors.text.secondary};
`;

const Toggle = styled.input`
  width: 50px;
  height: 25px;
  appearance: none;
  background: ${props => props.theme.colors.gray[300]};
  border-radius: 25px;
  position: relative;
  cursor: pointer;
  transition: background-color 0.2s ease;
  
  &:checked {
    background: ${props => props.theme.colors.primary};
  }
  
  &::before {
    content: '';
    position: absolute;
    width: 21px;
    height: 21px;
    border-radius: 50%;
    background: white;
    top: 2px;
    left: 2px;
    transition: transform 0.2s ease;
  }
  
  &:checked::before {
    transform: translateX(25px);
  }
`;

const PreferenceForm = styled.form`
  display: flex;
  flex-direction: column;
  gap: ${props => props.theme.spacing[4]};
`;

const FormRow = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: ${props => props.theme.spacing[4]};
`;

const Select = styled.select`
  width: 100%;
  padding: ${props => props.theme.spacing[3]} ${props => props.theme.spacing[4]};
  font-size: ${props => props.theme.fontSizes.base};
  font-family: ${props => props.theme.fonts.primary};
  background: ${props => props.theme.colors.white};
  border: 1px solid ${props => props.theme.colors.border.medium};
  border-radius: ${props => props.theme.borderRadius.lg};
  color: ${props => props.theme.colors.text.primary};
  
  &:focus {
    outline: none;
    border-color: ${props => props.theme.colors.primary};
    box-shadow: 0 0 0 3px ${props => props.theme.colors.primary}20;
  }
`;

const DangerZone = styled(Card)`
  padding: ${props => props.theme.spacing[6]};
  border: 1px solid ${props => props.theme.colors.error};
  background: ${props => props.theme.colors.error}05;
`;

const SettingsPage = () => {
  const [preferences, setPreferences] = useState({
    showAge: true,
    showLocation: true,
    showMissionInfo: true,
    ageRangeMin: 18,
    ageRangeMax: 35,
    maxDistance: 50,
    activityLevel: 'any'
  });
  const [logoutModalOpen, setLogoutModalOpen] = useState(false);
  const { logout, userProfile } = useAuth();

  const handleToggle = (setting) => {
    setPreferences(prev => ({
      ...prev,
      [setting]: !prev[setting]
    }));
  };

  const handleLogout = async () => {
    await logout();
    setLogoutModalOpen(false);
  };

  return (
    <SettingsContainer>
      <Header>
        <Title>Settings</Title>
      </Header>

      <SettingsSection>
        <SectionTitle>
          <FiShield />
          Privacy Settings
        </SectionTitle>
        
        <SettingItem>
          <SettingInfo>
            <SettingLabel>Show Age</SettingLabel>
            <SettingDescription>Display your age on your profile</SettingDescription>
          </SettingInfo>
          <Toggle
            type="checkbox"
            checked={preferences.showAge}
            onChange={() => handleToggle('showAge')}
          />
        </SettingItem>
        
        <SettingItem>
          <SettingInfo>
            <SettingLabel>Show Location</SettingLabel>
            <SettingDescription>Display your city and country</SettingDescription>
          </SettingInfo>
          <Toggle
            type="checkbox"
            checked={preferences.showLocation}
            onChange={() => handleToggle('showLocation')}
          />
        </SettingItem>
        
        <SettingItem>
          <SettingInfo>
            <SettingLabel>Show Mission Information</SettingLabel>
            <SettingDescription>Display your mission details if you're a returned missionary</SettingDescription>
          </SettingInfo>
          <Toggle
            type="checkbox"
            checked={preferences.showMissionInfo}
            onChange={() => handleToggle('showMissionInfo')}
          />
        </SettingItem>
      </SettingsSection>

      <SettingsSection>
        <SectionTitle>
          <FiHeart />
          Discovery Preferences
        </SectionTitle>
        
        <PreferenceForm>
          <FormRow>
            <div>
              <label>Age Range</label>
              <div style={{ display: 'flex', gap: '12px', alignItems: 'center', marginTop: '8px' }}>
                <Input
                  type="number"
                  value={preferences.ageRangeMin}
                  onChange={(e) => setPreferences(prev => ({ ...prev, ageRangeMin: parseInt(e.target.value) }))}
                  min="18"
                  max="100"
                />
                <span>to</span>
                <Input
                  type="number"
                  value={preferences.ageRangeMax}
                  onChange={(e) => setPreferences(prev => ({ ...prev, ageRangeMax: parseInt(e.target.value) }))}
                  min="18"
                  max="100"
                />
              </div>
            </div>
            
            <div>
              <label>Maximum Distance (miles)</label>
              <Input
                type="number"
                value={preferences.maxDistance}
                onChange={(e) => setPreferences(prev => ({ ...prev, maxDistance: parseInt(e.target.value) }))}
                min="1"
                max="500"
              />
            </div>
          </FormRow>
          
          <div>
            <label>Preferred Activity Level</label>
            <Select
              value={preferences.activityLevel}
              onChange={(e) => setPreferences(prev => ({ ...prev, activityLevel: e.target.value }))}
            >
              <option value="any">Any Activity Level</option>
              <option value="very-active">Very Active</option>
              <option value="active">Active</option>
              <option value="somewhat-active">Somewhat Active</option>
              <option value="less-active">Less Active</option>
            </Select>
          </div>
          
          <Button>Save Preferences</Button>
        </PreferenceForm>
      </SettingsSection>

      <SettingsSection>
        <SectionTitle>
          <FiUser />
          Account
        </SectionTitle>
        
        <SettingItem>
          <SettingInfo>
            <SettingLabel>Email</SettingLabel>
            <SettingDescription>{userProfile?.email}</SettingDescription>
          </SettingInfo>
          <Button variant="outline" size="sm">Change</Button>
        </SettingItem>
        
        <SettingItem>
          <SettingInfo>
            <SettingLabel>Password</SettingLabel>
            <SettingDescription>Last changed 30 days ago</SettingDescription>
          </SettingInfo>
          <Button variant="outline" size="sm">Change</Button>
        </SettingItem>
      </SettingsSection>

      <DangerZone>
        <SectionTitle style={{ color: '#ED4956' }}>
          <FiLogOut />
          Account Actions
        </SectionTitle>
        
        <SettingItem>
          <SettingInfo>
            <SettingLabel>Sign Out</SettingLabel>
            <SettingDescription>Sign out of your account</SettingDescription>
          </SettingInfo>
          <Button variant="danger" onClick={() => setLogoutModalOpen(true)}>
            Sign Out
          </Button>
        </SettingItem>
      </DangerZone>

      <Modal
        isOpen={logoutModalOpen}
        onClose={() => setLogoutModalOpen(false)}
        title="Sign Out"
        footer={
          <>
            <Button variant="outline" onClick={() => setLogoutModalOpen(false)}>
              Cancel
            </Button>
            <Button variant="danger" onClick={handleLogout}>
              Sign Out
            </Button>
          </>
        }
      >
        <p>Are you sure you want to sign out of your account?</p>
      </Modal>
    </SettingsContainer>
  );
};

export default SettingsPage;
