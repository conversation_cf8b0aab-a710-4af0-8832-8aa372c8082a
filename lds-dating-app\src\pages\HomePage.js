import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { motion, AnimatePresence } from 'framer-motion';
import { FiHeart, FiX, FiMapPin, FiCalendar, FiInfo } from 'react-icons/fi';
import { useMatch } from '../contexts/MatchContext';
import { useAuth } from '../contexts/AuthContext';
import { getRandomQuote } from '../data/ldsContent';
import Button from '../components/UI/Button';
import Card from '../components/UI/Card';
import Avatar from '../components/UI/Avatar';
import DailyInspiration from '../components/DailyInspiration';

const HomeContainer = styled.div`
  max-width: 400px;
  margin: 0 auto;
  position: relative;
`;

const Header = styled.div`
  text-align: center;
  margin-bottom: ${props => props.theme.spacing[6]};
`;

const Title = styled.h1`
  font-size: ${props => props.theme.fontSizes['2xl']};
  font-weight: ${props => props.theme.fontWeights.bold};
  color: ${props => props.theme.colors.text.primary};
  margin-bottom: ${props => props.theme.spacing[2]};
`;

const Subtitle = styled.p`
  color: ${props => props.theme.colors.text.secondary};
`;

const CardStack = styled.div`
  position: relative;
  height: 600px;
  margin-bottom: ${props => props.theme.spacing[6]};
`;

const ProfileCard = styled(motion.div)`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 100%;
  cursor: grab;
  
  &:active {
    cursor: grabbing;
  }
`;

const ProfileImage = styled.div`
  height: 70%;
  background: ${props => `url(${props.src}) center/cover`};
  background-color: ${props => props.theme.colors.gray[200]};
  border-radius: ${props => props.theme.borderRadius['2xl']} ${props => props.theme.borderRadius['2xl']} 0 0;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  color: ${props => props.theme.colors.text.secondary};
  font-size: ${props => props.theme.fontSizes['4xl']};
`;

const ProfileInfo = styled.div`
  padding: ${props => props.theme.spacing[6]};
  height: 30%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
`;

const ProfileHeader = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: ${props => props.theme.spacing[3]};
`;

const ProfileName = styled.h2`
  font-size: ${props => props.theme.fontSizes.xl};
  font-weight: ${props => props.theme.fontWeights.semibold};
  color: ${props => props.theme.colors.text.primary};
  margin: 0;
`;

const ProfileAge = styled.span`
  font-size: ${props => props.theme.fontSizes.lg};
  color: ${props => props.theme.colors.text.secondary};
  margin-left: ${props => props.theme.spacing[2]};
`;

const ProfileLocation = styled.div`
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing[1]};
  color: ${props => props.theme.colors.text.secondary};
  font-size: ${props => props.theme.fontSizes.sm};
  margin-bottom: ${props => props.theme.spacing[2]};
`;

const ProfileBio = styled.p`
  color: ${props => props.theme.colors.text.secondary};
  font-size: ${props => props.theme.fontSizes.sm};
  line-height: 1.5;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
`;

const ActionButtons = styled.div`
  display: flex;
  justify-content: center;
  gap: ${props => props.theme.spacing[6]};
`;

const ActionButton = styled(Button)`
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: ${props => props.theme.fontSizes.xl};
  box-shadow: ${props => props.theme.shadows.lg};
  
  ${props => props.variant === 'reject' && `
    background: ${props.theme.colors.white};
    color: ${props.theme.colors.gray[500]};
    border: 2px solid ${props.theme.colors.gray[300]};
    
    &:hover {
      background: ${props.theme.colors.gray[50]};
      color: ${props.theme.colors.gray[600]};
      transform: scale(1.1);
    }
  `}
  
  ${props => props.variant === 'like' && `
    background: linear-gradient(45deg, ${props.theme.colors.primary}, ${props.theme.colors.accent});
    color: ${props.theme.colors.white};
    
    &:hover {
      transform: scale(1.1);
      box-shadow: ${props.theme.shadows.xl};
    }
  `}
`;

const EmptyState = styled.div`
  text-align: center;
  padding: ${props => props.theme.spacing[12]} ${props => props.theme.spacing[6]};
`;

const EmptyStateIcon = styled.div`
  font-size: ${props => props.theme.fontSizes['4xl']};
  color: ${props => props.theme.colors.text.secondary};
  margin-bottom: ${props => props.theme.spacing[4]};
`;

const EmptyStateTitle = styled.h3`
  font-size: ${props => props.theme.fontSizes.xl};
  font-weight: ${props => props.theme.fontWeights.semibold};
  color: ${props => props.theme.colors.text.primary};
  margin-bottom: ${props => props.theme.spacing[2]};
`;

const EmptyStateText = styled.p`
  color: ${props => props.theme.colors.text.secondary};
  line-height: 1.6;
`;

const Quote = styled.div`
  text-align: center;
  margin-top: ${props => props.theme.spacing[8]};
  padding: ${props => props.theme.spacing[6]};
  background: linear-gradient(135deg, ${props => props.theme.colors.primary}10, ${props => props.theme.colors.accent}10);
  border-radius: ${props => props.theme.borderRadius.xl};
  border-left: 4px solid ${props => props.theme.colors.primary};
`;

const QuoteText = styled.p`
  font-style: italic;
  color: ${props => props.theme.colors.text.secondary};
  margin-bottom: ${props => props.theme.spacing[2]};
  font-size: ${props => props.theme.fontSizes.lg};
`;

const QuoteReference = styled.p`
  font-size: ${props => props.theme.fontSizes.sm};
  color: ${props => props.theme.colors.text.secondary};
  font-weight: ${props => props.theme.fontWeights.semibold};
`;

const HomePage = () => {
  const [profiles, setProfiles] = useState([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [loading, setLoading] = useState(true);
  const { getPotentialMatches, likeProfile, passProfile } = useMatch();
  const { userProfile } = useAuth();

  useEffect(() => {
    loadProfiles();
  }, []);

  const loadProfiles = async () => {
    try {
      setLoading(true);
      const matches = await getPotentialMatches();
      setProfiles(matches);
      setCurrentIndex(0);
    } catch (error) {
      console.error('Error loading profiles:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleLike = async () => {
    if (currentIndex < profiles.length) {
      const profile = profiles[currentIndex];
      await likeProfile(profile.id);
      nextProfile();
    }
  };

  const handlePass = async () => {
    if (currentIndex < profiles.length) {
      const profile = profiles[currentIndex];
      await passProfile(profile.id);
      nextProfile();
    }
  };

  const nextProfile = () => {
    setCurrentIndex(prev => prev + 1);
  };

  const currentProfile = profiles[currentIndex];
  const hasMoreProfiles = currentIndex < profiles.length;

  const randomQuote = getRandomQuote();

  if (loading) {
    return (
      <HomeContainer>
        <Header>
          <Title>Loading...</Title>
          <Subtitle>Finding your perfect matches</Subtitle>
        </Header>
      </HomeContainer>
    );
  }

  return (
    <HomeContainer>
      <Header>
        <Title>Discover</Title>
        <Subtitle>Find your eternal companion</Subtitle>
      </Header>

      {hasMoreProfiles ? (
        <>
          <CardStack>
            <AnimatePresence>
              {currentProfile && (
                <ProfileCard
                  key={currentProfile.id}
                  initial={{ scale: 0.9, opacity: 0 }}
                  animate={{ scale: 1, opacity: 1 }}
                  exit={{ scale: 0.9, opacity: 0 }}
                  transition={{ duration: 0.3 }}
                >
                  <Card hover>
                    <ProfileImage src={currentProfile.photos?.[0]}>
                      {!currentProfile.photos?.[0] && <FiInfo />}
                    </ProfileImage>
                    <ProfileInfo>
                      <div>
                        <ProfileHeader>
                          <div>
                            <ProfileName>
                              {currentProfile.displayName}
                              <ProfileAge>{currentProfile.age}</ProfileAge>
                            </ProfileName>
                          </div>
                        </ProfileHeader>
                        <ProfileLocation>
                          <FiMapPin />
                          {currentProfile.city}, {currentProfile.country}
                        </ProfileLocation>
                        <ProfileBio>{currentProfile.bio}</ProfileBio>
                      </div>
                    </ProfileInfo>
                  </Card>
                </ProfileCard>
              )}
            </AnimatePresence>
          </CardStack>

          <ActionButtons>
            <ActionButton variant="reject" onClick={handlePass}>
              <FiX />
            </ActionButton>
            <ActionButton variant="like" onClick={handleLike}>
              <FiHeart />
            </ActionButton>
          </ActionButtons>
        </>
      ) : (
        <EmptyState>
          <EmptyStateIcon>💕</EmptyStateIcon>
          <EmptyStateTitle>No More Profiles</EmptyStateTitle>
          <EmptyStateText>
            You've seen all available profiles in your area. Check back later for new members, 
            or adjust your preferences to see more matches.
          </EmptyStateText>
          <Button onClick={loadProfiles} style={{ marginTop: '24px' }}>
            Refresh
          </Button>
        </EmptyState>
      )}

      <DailyInspiration compact />
    </HomeContainer>
  );
};

export default HomePage;
