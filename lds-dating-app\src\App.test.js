import { render, screen } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { ThemeProvider } from 'styled-components';
import App from './App';
import theme from './styles/theme';

// Mock Firebase
jest.mock('./firebase/config', () => ({
  auth: {},
  db: {},
  storage: {}
}));

// Mock contexts
jest.mock('./contexts/AuthContext', () => ({
  AuthProvider: ({ children }) => children,
  useAuth: () => ({
    currentUser: null,
    userProfile: null,
    loading: false
  })
}));

jest.mock('./contexts/MatchContext', () => ({
  MatchProvider: ({ children }) => children,
  useMatch: () => ({
    matches: [],
    loading: false
  })
}));

const renderWithProviders = (component) => {
  return render(
    <ThemeProvider theme={theme}>
      <BrowserRouter>
        {component}
      </BrowserRouter>
    </ThemeProvider>
  );
};

test('renders app without crashing', () => {
  renderWithProviders(<App />);
});

test('renders landing page by default', () => {
  renderWithProviders(<App />);
  expect(screen.getByText('Find Your Eternal Companion')).toBeInTheDocument();
});
