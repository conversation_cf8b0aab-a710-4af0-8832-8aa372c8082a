rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can read and write their own user document
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
      // Allow other authenticated users to read basic profile info
      allow read: if request.auth != null;
    }
    
    // Likes collection - users can create likes and read their own
    match /likes/{likeId} {
      allow create: if request.auth != null && request.auth.uid == resource.data.fromUserId;
      allow read: if request.auth != null && 
        (request.auth.uid == resource.data.fromUserId || 
         request.auth.uid == resource.data.toUserId);
    }
    
    // Passes collection - users can create passes and read their own
    match /passes/{passId} {
      allow create: if request.auth != null && request.auth.uid == resource.data.fromUserId;
      allow read: if request.auth != null && request.auth.uid == resource.data.fromUserId;
    }
    
    // Matches collection - users can read matches they're part of
    match /matches/{matchId} {
      allow read, write: if request.auth != null && 
        request.auth.uid in resource.data.users;
    }
    
    // Conversations collection - users can read/write conversations they're part of
    match /conversations/{conversationId} {
      allow read, write: if request.auth != null && 
        request.auth.uid in resource.data.participants;
        
      // Messages subcollection
      match /messages/{messageId} {
        allow read, write: if request.auth != null && 
          request.auth.uid in get(/databases/$(database)/documents/conversations/$(conversationId)).data.participants;
      }
    }
  }
}
