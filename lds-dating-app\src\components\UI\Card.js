import styled, { css } from 'styled-components';

const Card = styled.div`
  background: ${props => props.theme.colors.white};
  border-radius: ${props => props.theme.borderRadius.xl};
  box-shadow: ${props => props.theme.shadows.sm};
  overflow: hidden;
  transition: all 0.2s ease-in-out;

  ${props => props.hover && css`
    &:hover {
      transform: translateY(-2px);
      box-shadow: ${props.theme.shadows.md};
    }
  `}

  ${props => props.padding && css`
    padding: ${props.theme.spacing[props.padding]};
  `}

  ${props => props.variant === 'elevated' && css`
    box-shadow: ${props.theme.shadows.lg};
  `}

  ${props => props.variant === 'outlined' && css`
    border: 1px solid ${props.theme.colors.border.light};
    box-shadow: none;
  `}

  ${props => props.clickable && css`
    cursor: pointer;
    
    &:hover {
      transform: translateY(-1px);
      box-shadow: ${props.theme.shadows.md};
    }
    
    &:active {
      transform: translateY(0);
    }
  `}
`;

const CardHeader = styled.div`
  padding: ${props => props.theme.spacing[4]} ${props => props.theme.spacing[4]} 0;
  
  ${props => props.noPadding && css`
    padding: 0;
  `}
`;

const CardBody = styled.div`
  padding: ${props => props.theme.spacing[4]};
  
  ${props => props.noPadding && css`
    padding: 0;
  `}
`;

const CardFooter = styled.div`
  padding: 0 ${props => props.theme.spacing[4]} ${props => props.theme.spacing[4]};
  
  ${props => props.noPadding && css`
    padding: 0;
  `}
`;

Card.Header = CardHeader;
Card.Body = CardBody;
Card.Footer = CardFooter;

export default Card;
