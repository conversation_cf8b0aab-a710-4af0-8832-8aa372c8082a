import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';

const ProtectedRoute = ({ children }) => {
  const { currentUser, userProfile } = useAuth();
  const location = useLocation();

  if (!currentUser) {
    // Redirect to login page with return url
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // If user hasn't completed profile setup, redirect to setup page
  if (userProfile && !userProfile.profileComplete && location.pathname !== '/setup') {
    return <Navigate to="/setup" replace />;
  }

  return children;
};

export default ProtectedRoute;
