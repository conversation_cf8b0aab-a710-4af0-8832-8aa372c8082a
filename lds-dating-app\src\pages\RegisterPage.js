import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import { <PERSON>Mail, <PERSON><PERSON>ock, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>yeOff } from 'react-icons/fi';
import { useAuth } from '../contexts/AuthContext';
import Button from '../components/UI/Button';
import Input from '../components/UI/Input';
import Card from '../components/UI/Card';

const RegisterContainer = styled.div`
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: ${props => props.theme.spacing[4]};
`;

const RegisterCard = styled(Card)`
  width: 100%;
  max-width: 500px;
  padding: ${props => props.theme.spacing[8]};
`;

const Logo = styled.h1`
  font-size: ${props => props.theme.fontSizes['3xl']};
  font-weight: ${props => props.theme.fontWeights.bold};
  background: linear-gradient(45deg, ${props => props.theme.colors.primary}, ${props => props.theme.colors.accent});
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-align: center;
  margin-bottom: ${props => props.theme.spacing[2]};
`;

const Subtitle = styled.p`
  text-align: center;
  color: ${props => props.theme.colors.text.secondary};
  margin-bottom: ${props => props.theme.spacing[8]};
`;

const Form = styled.form`
  display: flex;
  flex-direction: column;
  gap: ${props => props.theme.spacing[4]};
`;

const NameRow = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: ${props => props.theme.spacing[3]};
`;

const PasswordInputWrapper = styled.div`
  position: relative;
`;

const PasswordToggle = styled.button`
  position: absolute;
  right: ${props => props.theme.spacing[3]};
  top: 50%;
  transform: translateY(-50%);
  color: ${props => props.theme.colors.text.secondary};
  background: none;
  border: none;
  cursor: pointer;
  padding: ${props => props.theme.spacing[1]};
  
  &:hover {
    color: ${props => props.theme.colors.text.primary};
  }
`;

const PasswordStrength = styled.div`
  margin-top: ${props => props.theme.spacing[2]};
  display: flex;
  gap: ${props => props.theme.spacing[1]};
`;

const StrengthBar = styled.div`
  flex: 1;
  height: 4px;
  background: ${props => props.theme.colors.gray[200]};
  border-radius: 2px;
  
  ${props => props.active && `
    background: ${
      props.strength === 'weak' ? props.theme.colors.error :
      props.strength === 'medium' ? props.theme.colors.warning :
      props.theme.colors.success
    };
  `}
`;

const StrengthText = styled.p`
  font-size: ${props => props.theme.fontSizes.sm};
  margin-top: ${props => props.theme.spacing[1]};
  color: ${props => 
    props.strength === 'weak' ? props.theme.colors.error :
    props.strength === 'medium' ? props.theme.colors.warning :
    props.theme.colors.success
  };
`;

const Terms = styled.div`
  font-size: ${props => props.theme.fontSizes.sm};
  color: ${props => props.theme.colors.text.secondary};
  text-align: center;
  
  a {
    color: ${props => props.theme.colors.primary};
    
    &:hover {
      text-decoration: underline;
    }
  }
`;

const Divider = styled.div`
  display: flex;
  align-items: center;
  margin: ${props => props.theme.spacing[6]} 0;
  
  &::before,
  &::after {
    content: '';
    flex: 1;
    height: 1px;
    background: ${props => props.theme.colors.border.medium};
  }
  
  span {
    padding: 0 ${props => props.theme.spacing[4]};
    color: ${props => props.theme.colors.text.secondary};
    font-size: ${props => props.theme.fontSizes.sm};
  }
`;

const SignInLink = styled.div`
  text-align: center;
  color: ${props => props.theme.colors.text.secondary};
  
  a {
    color: ${props => props.theme.colors.primary};
    font-weight: ${props => props.theme.fontWeights.semibold};
    
    &:hover {
      text-decoration: underline;
    }
  }
`;

const Quote = styled.div`
  text-align: center;
  margin-top: ${props => props.theme.spacing[6]};
  padding: ${props => props.theme.spacing[4]};
  background: ${props => props.theme.colors.gray[50]};
  border-radius: ${props => props.theme.borderRadius.lg};
  border-left: 4px solid ${props => props.theme.colors.primary};
`;

const QuoteText = styled.p`
  font-style: italic;
  color: ${props => props.theme.colors.text.secondary};
  margin-bottom: ${props => props.theme.spacing[2]};
`;

const QuoteReference = styled.p`
  font-size: ${props => props.theme.fontSizes.sm};
  color: ${props => props.theme.colors.text.secondary};
  font-weight: ${props => props.theme.fontWeights.semibold};
`;

const RegisterPage = () => {
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [passwordStrength, setPasswordStrength] = useState('');
  const { register: registerUser } = useAuth();
  const navigate = useNavigate();

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
    setError
  } = useForm();

  const password = watch('password');

  const checkPasswordStrength = (password) => {
    if (!password) return '';
    if (password.length < 6) return 'weak';
    if (password.length < 10) return 'medium';
    return 'strong';
  };

  React.useEffect(() => {
    setPasswordStrength(checkPasswordStrength(password));
  }, [password]);

  const onSubmit = async (data) => {
    if (data.password !== data.confirmPassword) {
      setError('confirmPassword', {
        type: 'manual',
        message: 'Passwords do not match'
      });
      return;
    }

    try {
      setLoading(true);
      await registerUser(data.email, data.password, {
        firstName: data.firstName,
        lastName: data.lastName
      });
      navigate('/setup');
    } catch (error) {
      if (error.code === 'auth/email-already-in-use') {
        setError('email', {
          type: 'manual',
          message: 'This email is already registered'
        });
      } else {
        setError('email', {
          type: 'manual',
          message: 'Registration failed. Please try again.'
        });
      }
    } finally {
      setLoading(false);
    }
  };

  const ldsQuotes = [
    {
      text: "Marriage is ordained of God unto man. Neither is the man without the woman, neither the woman without the man, in the Lord.",
      reference: "1 Corinthians 11:11"
    },
    {
      text: "And they twain shall be one flesh: so then they are no more twain, but one flesh.",
      reference: "Mark 10:8"
    },
    {
      text: "Whoso findeth a wife findeth a good thing, and obtaineth favour of the Lord.",
      reference: "Proverbs 18:22"
    }
  ];

  const randomQuote = ldsQuotes[Math.floor(Math.random() * ldsQuotes.length)];

  return (
    <RegisterContainer>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <RegisterCard>
          <Logo>LDS Hearts</Logo>
          <Subtitle>Create your account and start your journey to find your eternal companion.</Subtitle>
          
          <Form onSubmit={handleSubmit(onSubmit)}>
            <NameRow>
              <Input
                type="text"
                placeholder="First Name"
                icon={<FiUser />}
                error={errors.firstName?.message}
                {...register('firstName', {
                  required: 'First name is required',
                  minLength: {
                    value: 2,
                    message: 'First name must be at least 2 characters'
                  }
                })}
              />
              <Input
                type="text"
                placeholder="Last Name"
                icon={<FiUser />}
                error={errors.lastName?.message}
                {...register('lastName', {
                  required: 'Last name is required',
                  minLength: {
                    value: 2,
                    message: 'Last name must be at least 2 characters'
                  }
                })}
              />
            </NameRow>
            
            <Input
              type="email"
              placeholder="Email address"
              icon={<FiMail />}
              error={errors.email?.message}
              {...register('email', {
                required: 'Email is required',
                pattern: {
                  value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                  message: 'Invalid email address'
                }
              })}
            />
            
            <PasswordInputWrapper>
              <Input
                type={showPassword ? 'text' : 'password'}
                placeholder="Password"
                icon={<FiLock />}
                error={errors.password?.message}
                {...register('password', {
                  required: 'Password is required',
                  minLength: {
                    value: 6,
                    message: 'Password must be at least 6 characters'
                  }
                })}
              />
              <PasswordToggle
                type="button"
                onClick={() => setShowPassword(!showPassword)}
              >
                {showPassword ? <FiEyeOff /> : <FiEye />}
              </PasswordToggle>
            </PasswordInputWrapper>
            
            {password && (
              <>
                <PasswordStrength>
                  <StrengthBar active={passwordStrength !== ''} strength={passwordStrength} />
                  <StrengthBar active={passwordStrength === 'medium' || passwordStrength === 'strong'} strength={passwordStrength} />
                  <StrengthBar active={passwordStrength === 'strong'} strength={passwordStrength} />
                </PasswordStrength>
                <StrengthText strength={passwordStrength}>
                  Password strength: {passwordStrength}
                </StrengthText>
              </>
            )}
            
            <PasswordInputWrapper>
              <Input
                type={showConfirmPassword ? 'text' : 'password'}
                placeholder="Confirm Password"
                icon={<FiLock />}
                error={errors.confirmPassword?.message}
                {...register('confirmPassword', {
                  required: 'Please confirm your password'
                })}
              />
              <PasswordToggle
                type="button"
                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
              >
                {showConfirmPassword ? <FiEyeOff /> : <FiEye />}
              </PasswordToggle>
            </PasswordInputWrapper>
            
            <Terms>
              By creating an account, you agree to our{' '}
              <Link to="/terms">Terms of Service</Link> and{' '}
              <Link to="/privacy">Privacy Policy</Link>
            </Terms>
            
            <Button type="submit" loading={loading} fullWidth>
              Create Account
            </Button>
          </Form>
          
          <Divider>
            <span>or</span>
          </Divider>
          
          <SignInLink>
            Already have an account? <Link to="/login">Sign in here</Link>
          </SignInLink>
          
          <Quote>
            <QuoteText>"{randomQuote.text}"</QuoteText>
            <QuoteReference>— {randomQuote.reference}</QuoteReference>
          </Quote>
        </RegisterCard>
      </motion.div>
    </RegisterContainer>
  );
};

export default RegisterPage;
