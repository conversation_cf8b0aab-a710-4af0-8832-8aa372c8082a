import React, { forwardRef } from 'react';
import styled, { css } from 'styled-components';

const InputWrapper = styled.div`
  position: relative;
  width: 100%;
`;

const InputField = styled.input`
  width: 100%;
  padding: ${props => props.theme.spacing[3]} ${props => props.theme.spacing[4]};
  font-size: ${props => props.theme.fontSizes.base};
  font-family: ${props => props.theme.fonts.primary};
  background: ${props => props.theme.colors.white};
  border: 1px solid ${props => props.theme.colors.border.medium};
  border-radius: ${props => props.theme.borderRadius.lg};
  transition: all 0.2s ease-in-out;
  color: ${props => props.theme.colors.text.primary};

  &::placeholder {
    color: ${props => props.theme.colors.text.secondary};
  }

  &:focus {
    outline: none;
    border-color: ${props => props.theme.colors.primary};
    box-shadow: 0 0 0 3px ${props => props.theme.colors.primary}20;
  }

  &:disabled {
    background: ${props => props.theme.colors.gray[100]};
    cursor: not-allowed;
    opacity: 0.6;
  }

  ${props => props.error && css`
    border-color: ${props.theme.colors.error};
    
    &:focus {
      border-color: ${props.theme.colors.error};
      box-shadow: 0 0 0 3px ${props.theme.colors.error}20;
    }
  `}

  ${props => props.size === 'sm' && css`
    padding: ${props.theme.spacing[2]} ${props.theme.spacing[3]};
    font-size: ${props.theme.fontSizes.sm};
  `}

  ${props => props.size === 'lg' && css`
    padding: ${props.theme.spacing[4]} ${props.theme.spacing[5]};
    font-size: ${props.theme.fontSizes.lg};
  `}
`;

const TextArea = styled.textarea`
  width: 100%;
  padding: ${props => props.theme.spacing[3]} ${props => props.theme.spacing[4]};
  font-size: ${props => props.theme.fontSizes.base};
  font-family: ${props => props.theme.fonts.primary};
  background: ${props => props.theme.colors.white};
  border: 1px solid ${props => props.theme.colors.border.medium};
  border-radius: ${props => props.theme.borderRadius.lg};
  transition: all 0.2s ease-in-out;
  color: ${props => props.theme.colors.text.primary};
  resize: vertical;
  min-height: 100px;

  &::placeholder {
    color: ${props => props.theme.colors.text.secondary};
  }

  &:focus {
    outline: none;
    border-color: ${props => props.theme.colors.primary};
    box-shadow: 0 0 0 3px ${props => props.theme.colors.primary}20;
  }

  &:disabled {
    background: ${props => props.theme.colors.gray[100]};
    cursor: not-allowed;
    opacity: 0.6;
  }

  ${props => props.error && css`
    border-color: ${props.theme.colors.error};
    
    &:focus {
      border-color: ${props.theme.colors.error};
      box-shadow: 0 0 0 3px ${props.theme.colors.error}20;
    }
  `}
`;

const Label = styled.label`
  display: block;
  font-size: ${props => props.theme.fontSizes.sm};
  font-weight: ${props => props.theme.fontWeights.medium};
  color: ${props => props.theme.colors.text.primary};
  margin-bottom: ${props => props.theme.spacing[2]};
`;

const ErrorMessage = styled.span`
  display: block;
  font-size: ${props => props.theme.fontSizes.sm};
  color: ${props => props.theme.colors.error};
  margin-top: ${props => props.theme.spacing[1]};
`;

const HelperText = styled.span`
  display: block;
  font-size: ${props => props.theme.fontSizes.sm};
  color: ${props => props.theme.colors.text.secondary};
  margin-top: ${props => props.theme.spacing[1]};
`;

const IconWrapper = styled.div`
  position: absolute;
  right: ${props => props.theme.spacing[3]};
  top: 50%;
  transform: translateY(-50%);
  color: ${props => props.theme.colors.text.secondary};
  pointer-events: none;
`;

const Input = forwardRef(({
  label,
  error,
  helperText,
  icon,
  multiline = false,
  rows = 4,
  size = 'md',
  ...props
}, ref) => {
  const Component = multiline ? TextArea : InputField;

  return (
    <InputWrapper>
      {label && <Label>{label}</Label>}
      <div style={{ position: 'relative' }}>
        <Component
          ref={ref}
          error={error}
          size={size}
          rows={multiline ? rows : undefined}
          {...props}
        />
        {icon && <IconWrapper>{icon}</IconWrapper>}
      </div>
      {error && <ErrorMessage>{error}</ErrorMessage>}
      {helperText && !error && <HelperText>{helperText}</HelperText>}
    </InputWrapper>
  );
});

Input.displayName = 'Input';

export default Input;
