{"hosting": {"public": "build", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "rewrites": [{"source": "**", "destination": "/index.html"}], "headers": [{"source": "/static/**", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "**/*.@(js|css)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}]}, "firestore": {"rules": "firestore.rules", "indexes": "firestore.indexes.json"}, "storage": {"rules": "storage.rules"}}