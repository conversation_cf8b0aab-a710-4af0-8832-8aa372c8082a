import React, { createContext, useContext, useState, useEffect } from 'react';
import { 
  collection, 
  doc, 
  setDoc, 
  getDoc, 
  getDocs, 
  query, 
  where, 
  orderBy, 
  onSnapshot,
  updateDoc,
  arrayUnion,
  arrayRemove,
  serverTimestamp
} from 'firebase/firestore';
import { db } from '../firebase/config';
import { useAuth } from './AuthContext';
import toast from 'react-hot-toast';

const MatchContext = createContext();

export const useMatch = () => {
  const context = useContext(MatchContext);
  if (!context) {
    throw new Error('useMatch must be used within a MatchProvider');
  }
  return context;
};

export const MatchProvider = ({ children }) => {
  const { currentUser } = useAuth();
  const [matches, setMatches] = useState([]);
  const [likedProfiles, setLikedProfiles] = useState([]);
  const [conversations, setConversations] = useState([]);
  const [loading, setLoading] = useState(false);

  // Like a profile
  const likeProfile = async (targetUserId) => {
    try {
      if (!currentUser) return;

      const likeData = {
        fromUserId: currentUser.uid,
        toUserId: targetUserId,
        createdAt: serverTimestamp(),
        type: 'like'
      };

      // Add like to likes collection
      await setDoc(doc(db, 'likes', `${currentUser.uid}_${targetUserId}`), likeData);

      // Check if target user has already liked current user
      const reciprocalLike = await getDoc(doc(db, 'likes', `${targetUserId}_${currentUser.uid}`));
      
      if (reciprocalLike.exists()) {
        // It's a match!
        await createMatch(currentUser.uid, targetUserId);
        toast.success("It's a match! 💕");
      } else {
        toast.success('Profile liked!');
      }

      // Update local state
      setLikedProfiles(prev => [...prev, targetUserId]);
      
    } catch (error) {
      console.error('Error liking profile:', error);
      toast.error('Error liking profile');
    }
  };

  // Pass on a profile
  const passProfile = async (targetUserId) => {
    try {
      if (!currentUser) return;

      const passData = {
        fromUserId: currentUser.uid,
        toUserId: targetUserId,
        createdAt: serverTimestamp(),
        type: 'pass'
      };

      // Add pass to passes collection
      await setDoc(doc(db, 'passes', `${currentUser.uid}_${targetUserId}`), passData);
      
    } catch (error) {
      console.error('Error passing profile:', error);
    }
  };

  // Create a match
  const createMatch = async (userId1, userId2) => {
    try {
      const matchId = [userId1, userId2].sort().join('_');
      
      const matchData = {
        id: matchId,
        users: [userId1, userId2],
        createdAt: serverTimestamp(),
        lastMessage: null,
        lastMessageTime: null,
        unreadCount: {
          [userId1]: 0,
          [userId2]: 0
        }
      };

      await setDoc(doc(db, 'matches', matchId), matchData);
      
      // Create conversation document
      const conversationData = {
        id: matchId,
        participants: [userId1, userId2],
        createdAt: serverTimestamp(),
        lastMessage: null,
        lastMessageTime: null,
        messages: []
      };

      await setDoc(doc(db, 'conversations', matchId), conversationData);
      
    } catch (error) {
      console.error('Error creating match:', error);
    }
  };

  // Get user matches
  const getUserMatches = async () => {
    try {
      if (!currentUser) return [];

      const matchesQuery = query(
        collection(db, 'matches'),
        where('users', 'array-contains', currentUser.uid),
        orderBy('createdAt', 'desc')
      );

      const matchesSnapshot = await getDocs(matchesQuery);
      const matchesData = matchesSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));

      return matchesData;
    } catch (error) {
      console.error('Error getting matches:', error);
      return [];
    }
  };

  // Get potential matches (users to show in discovery)
  const getPotentialMatches = async (filters = {}) => {
    try {
      if (!currentUser) return [];

      setLoading(true);

      // Get users that current user has already liked or passed
      const likedQuery = query(
        collection(db, 'likes'),
        where('fromUserId', '==', currentUser.uid)
      );
      const passedQuery = query(
        collection(db, 'passes'),
        where('fromUserId', '==', currentUser.uid)
      );

      const [likedSnapshot, passedSnapshot] = await Promise.all([
        getDocs(likedQuery),
        getDocs(passedQuery)
      ]);

      const excludedUserIds = [
        ...likedSnapshot.docs.map(doc => doc.data().toUserId),
        ...passedSnapshot.docs.map(doc => doc.data().toUserId),
        currentUser.uid // Exclude self
      ];

      // Get all users except excluded ones
      const usersQuery = query(
        collection(db, 'users'),
        where('profileComplete', '==', true)
      );

      const usersSnapshot = await getDocs(usersQuery);
      const potentialMatches = usersSnapshot.docs
        .map(doc => ({ id: doc.id, ...doc.data() }))
        .filter(user => !excludedUserIds.includes(user.id))
        .filter(user => {
          // Apply filters
          if (filters.ageRange) {
            const age = user.age;
            if (age < filters.ageRange.min || age > filters.ageRange.max) {
              return false;
            }
          }
          if (filters.location && user.city !== filters.location) {
            return false;
          }
          if (filters.activityLevel && user.activityLevel !== filters.activityLevel) {
            return false;
          }
          return true;
        });

      setLoading(false);
      return potentialMatches;
    } catch (error) {
      console.error('Error getting potential matches:', error);
      setLoading(false);
      return [];
    }
  };

  // Listen to matches in real-time
  useEffect(() => {
    if (!currentUser) return;

    const matchesQuery = query(
      collection(db, 'matches'),
      where('users', 'array-contains', currentUser.uid),
      orderBy('createdAt', 'desc')
    );

    const unsubscribe = onSnapshot(matchesQuery, (snapshot) => {
      const matchesData = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));
      setMatches(matchesData);
    });

    return unsubscribe;
  }, [currentUser]);

  const value = {
    matches,
    likedProfiles,
    conversations,
    loading,
    likeProfile,
    passProfile,
    getUserMatches,
    getPotentialMatches
  };

  return (
    <MatchContext.Provider value={value}>
      {children}
    </MatchContext.Provider>
  );
};
