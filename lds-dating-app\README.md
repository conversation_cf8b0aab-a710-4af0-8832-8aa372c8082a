# LDS Hearts - Dating App for LDS Church Members

A modern, Instagram-like dating application specifically designed for members of The Church of Jesus Christ of Latter-day Saints. Built with React.js, Firebase, and styled-components.

## Features

### 🎯 Core Features
- **Faith-Based Matching**: Connect with fellow LDS members who share your values
- **Instagram-Like UI**: Modern, familiar interface with swipe functionality
- **LDS-Specific Profiles**: Ward, Stake, mission service, activity level, and more
- **Real-Time Messaging**: Chat with matches instantly
- **Daily Inspiration**: LDS quotes, scriptures, and spiritual content
- **Safe & Secure**: Profile verification and privacy controls

### 📱 User Experience
- **Responsive Design**: Works perfectly on mobile and desktop
- **Progressive Web App**: Install on your device for native app experience
- **Offline Support**: Basic functionality works without internet
- **Fast Loading**: Optimized performance with code splitting

### 🏛️ LDS-Specific Features
- **Mission Information**: Share your mission experience
- **Ward & Stake Details**: Connect with local members
- **Activity Level**: Find members with similar commitment levels
- **Temple Preferences**: Share your temple worship preferences
- **Gospel-Centered Content**: Daily scriptures and inspirational quotes
- **Conversation Starters**: LDS-themed ice breakers

## Technology Stack

- **Frontend**: React 18, React Router, Styled Components
- **Backend**: Firebase (Authentication, Firestore, Storage)
- **UI/UX**: Framer Motion, React Icons, React Hook Form
- **Testing**: Jest, React Testing Library
- **Build**: Create React App, ESLint, Prettier

## Getting Started

### Prerequisites
- Node.js 16+ and npm
- Firebase account and project
- Git

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/your-username/lds-dating-app.git
   cd lds-dating-app
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Configure Firebase**
   - Create a Firebase project at https://console.firebase.google.com
   - Enable Authentication (Email/Password)
   - Create a Firestore database
   - Enable Storage
   - Copy your Firebase config to `src/firebase/config.js`

4. **Start development server**
   ```bash
   npm start
   ```

5. **Open your browser**
   Navigate to `http://localhost:3000`

### Firebase Configuration

Replace the placeholder values in `src/firebase/config.js`:

```javascript
const firebaseConfig = {
  apiKey: "your-api-key",
  authDomain: "your-project.firebaseapp.com",
  projectId: "your-project-id",
  storageBucket: "your-project.appspot.com",
  messagingSenderId: "your-sender-id",
  appId: "your-app-id"
};
```

## Project Structure

```
src/
├── components/          # Reusable UI components
│   ├── UI/             # Basic UI components (Button, Input, etc.)
│   ├── Layout.js       # App layout with navigation
│   └── DailyInspiration.js
├── contexts/           # React contexts for state management
│   ├── AuthContext.js  # Authentication state
│   └── MatchContext.js # Matching and messaging state
├── data/              # Static data and content
│   └── ldsContent.js  # LDS quotes, scriptures, inspiration
├── pages/             # Page components
│   ├── LandingPage.js
│   ├── LoginPage.js
│   ├── RegisterPage.js
│   ├── ProfileSetupPage.js
│   ├── HomePage.js
│   ├── ProfilePage.js
│   ├── MatchesPage.js
│   ├── MessagesPage.js
│   └── SettingsPage.js
├── styles/            # Global styles and theme
│   ├── theme.js
│   └── GlobalStyles.js
└── firebase/          # Firebase configuration
    └── config.js
```

## Available Scripts

- `npm start` - Start development server
- `npm test` - Run test suite
- `npm run build` - Build for production
- `npm run eject` - Eject from Create React App (not recommended)

## Testing

Run the test suite:
```bash
npm test
```

Run tests with coverage:
```bash
npm test -- --coverage
```

## Deployment

### Firebase Hosting (Recommended)

1. **Install Firebase CLI**
   ```bash
   npm install -g firebase-tools
   ```

2. **Login to Firebase**
   ```bash
   firebase login
   ```

3. **Initialize Firebase Hosting**
   ```bash
   firebase init hosting
   ```

4. **Build and deploy**
   ```bash
   npm run build
   firebase deploy
   ```

### Other Deployment Options
- **Netlify**: Connect your GitHub repo for automatic deployments
- **Vercel**: Import project from GitHub
- **AWS S3**: Upload build folder to S3 bucket with static hosting

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Acknowledgments

- The Church of Jesus Christ of Latter-day Saints for inspiration
- React and Firebase teams for excellent tools
- LDS community for feedback and support

## Support

For support, email <EMAIL> or create an issue on GitHub.

---

**Note**: This app is created by and for members of The Church of Jesus Christ of Latter-day Saints. It is not officially affiliated with the Church.
