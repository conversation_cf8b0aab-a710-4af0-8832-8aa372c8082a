import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import { FiHeart, FiUsers, FiShield, FiStar, FiArrowRight, FiCheck } from 'react-icons/fi';
import Button from '../components/UI/Button';
import Card from '../components/UI/Card';

const LandingContainer = styled.div`
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow-x: hidden;
`;

const Header = styled.header`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid ${props => props.theme.colors.border.light};
  z-index: ${props => props.theme.zIndex.sticky};
  padding: ${props => props.theme.spacing[4]} 0;
`;

const HeaderContent = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 ${props => props.theme.spacing[4]};
  display: flex;
  align-items: center;
  justify-content: space-between;
`;

const Logo = styled.h1`
  font-size: ${props => props.theme.fontSizes['2xl']};
  font-weight: ${props => props.theme.fontWeights.bold};
  background: linear-gradient(45deg, ${props => props.theme.colors.primary}, ${props => props.theme.colors.accent});
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0;
`;

const HeaderButtons = styled.div`
  display: flex;
  gap: ${props => props.theme.spacing[3]};
`;

const HeroSection = styled.section`
  padding: 120px 0 80px;
  text-align: center;
  position: relative;
`;

const HeroContent = styled.div`
  max-width: 800px;
  margin: 0 auto;
  padding: 0 ${props => props.theme.spacing[4]};
`;

const HeroTitle = styled(motion.h1)`
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: ${props => props.theme.fontWeights.bold};
  color: ${props => props.theme.colors.white};
  margin-bottom: ${props => props.theme.spacing[6]};
  line-height: 1.2;
`;

const HeroSubtitle = styled(motion.p)`
  font-size: ${props => props.theme.fontSizes.xl};
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: ${props => props.theme.spacing[8]};
  line-height: 1.6;
`;

const HeroCTA = styled(motion.div)`
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: ${props => props.theme.spacing[4]};

  @media (min-width: ${props => props.theme.breakpoints.sm}) {
    flex-direction: row;
    justify-content: center;
  }
`;

const Section = styled.section`
  padding: 80px 0;
  background: ${props => props.theme.colors.white};
`;

const SectionContent = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 ${props => props.theme.spacing[4]};
`;

const SectionTitle = styled.h2`
  font-size: ${props => props.theme.fontSizes['3xl']};
  font-weight: ${props => props.theme.fontWeights.bold};
  text-align: center;
  margin-bottom: ${props => props.theme.spacing[4]};
  color: ${props => props.theme.colors.text.primary};
`;

const SectionSubtitle = styled.p`
  font-size: ${props => props.theme.fontSizes.lg};
  text-align: center;
  color: ${props => props.theme.colors.text.secondary};
  margin-bottom: ${props => props.theme.spacing[12]};
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
`;

const FeaturesGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: ${props => props.theme.spacing[8]};
  margin-bottom: ${props => props.theme.spacing[12]};
`;

const FeatureCard = styled(Card)`
  text-align: center;
  padding: ${props => props.theme.spacing[8]};
  transition: transform 0.3s ease;

  &:hover {
    transform: translateY(-8px);
  }
`;

const FeatureIcon = styled.div`
  width: 80px;
  height: 80px;
  background: linear-gradient(45deg, ${props => props.theme.colors.primary}, ${props => props.theme.colors.accent});
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto ${props => props.theme.spacing[6]};
  color: ${props => props.theme.colors.white};
  font-size: ${props => props.theme.fontSizes['2xl']};
`;

const FeatureTitle = styled.h3`
  font-size: ${props => props.theme.fontSizes.xl};
  font-weight: ${props => props.theme.fontWeights.semibold};
  margin-bottom: ${props => props.theme.spacing[3]};
  color: ${props => props.theme.colors.text.primary};
`;

const FeatureDescription = styled.p`
  color: ${props => props.theme.colors.text.secondary};
  line-height: 1.6;
`;

const StatsSection = styled.section`
  padding: 80px 0;
  background: linear-gradient(135deg, ${props => props.theme.colors.primary}, ${props => props.theme.colors.accent});
`;

const StatsGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: ${props => props.theme.spacing[8]};
  text-align: center;
`;

const StatCard = styled.div`
  color: ${props => props.theme.colors.white};
`;

const StatNumber = styled.div`
  font-size: ${props => props.theme.fontSizes['4xl']};
  font-weight: ${props => props.theme.fontWeights.bold};
  margin-bottom: ${props => props.theme.spacing[2]};
`;

const StatLabel = styled.div`
  font-size: ${props => props.theme.fontSizes.lg};
  opacity: 0.9;
`;

const CTASection = styled.section`
  padding: 100px 0;
  background: ${props => props.theme.colors.gray[50]};
  text-align: center;
`;

const CTATitle = styled.h2`
  font-size: ${props => props.theme.fontSizes['4xl']};
  font-weight: ${props => props.theme.fontWeights.bold};
  margin-bottom: ${props => props.theme.spacing[6]};
  color: ${props => props.theme.colors.text.primary};
`;

const CTASubtitle = styled.p`
  font-size: ${props => props.theme.fontSizes.xl};
  color: ${props => props.theme.colors.text.secondary};
  margin-bottom: ${props => props.theme.spacing[8]};
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
`;

const Footer = styled.footer`
  background: ${props => props.theme.colors.gray[900]};
  color: ${props => props.theme.colors.white};
  padding: 60px 0 30px;
  text-align: center;
`;

const LandingPage = () => {
  const navigate = useNavigate();

  const features = [
    {
      icon: FiHeart,
      title: "Faith-Based Matching",
      description: "Connect with fellow Latter-day Saints who share your values and beliefs. Our algorithm considers your spiritual goals and preferences."
    },
    {
      icon: FiShield,
      title: "Safe & Secure",
      description: "Your privacy and safety are our top priorities. All profiles are verified and we maintain the highest security standards."
    },
    {
      icon: FiUsers,
      title: "Meaningful Connections",
      description: "Move beyond superficial swipes. Build genuine relationships based on shared faith, values, and life goals."
    },
    {
      icon: FiStar,
      title: "Success Stories",
      description: "Join thousands of LDS singles who have found their eternal companions through our platform."
    }
  ];

  const stats = [
    { number: "10K+", label: "Active Members" },
    { number: "2.5K+", label: "Success Stories" },
    { number: "95%", label: "Satisfaction Rate" },
    { number: "24/7", label: "Support Available" }
  ];

  return (
    <LandingContainer>
      <Header>
        <HeaderContent>
          <Logo>LDS Hearts</Logo>
          <HeaderButtons>
            <Button variant="ghost" onClick={() => navigate('/login')}>
              Sign In
            </Button>
            <Button onClick={() => navigate('/register')}>
              Get Started
            </Button>
          </HeaderButtons>
        </HeaderContent>
      </Header>

      <HeroSection>
        <HeroContent>
          <HeroTitle
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            Find Your Eternal Companion
          </HeroTitle>
          <HeroSubtitle
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            Connect with faithful LDS singles who share your values, dreams, and commitment to building an eternal family together.
          </HeroSubtitle>
          <HeroCTA
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
          >
            <Button size="lg" onClick={() => navigate('/register')}>
              Start Your Journey <FiArrowRight />
            </Button>
            <Button variant="outline" size="lg" onClick={() => navigate('/login')}>
              Already a Member?
            </Button>
          </HeroCTA>
        </HeroContent>
      </HeroSection>

      <Section>
        <SectionContent>
          <SectionTitle>Why Choose LDS Hearts?</SectionTitle>
          <SectionSubtitle>
            We understand the importance of finding someone who shares your faith and values. Our platform is designed specifically for LDS singles.
          </SectionSubtitle>
          <FeaturesGrid>
            {features.map((feature, index) => (
              <FeatureCard key={index}>
                <FeatureIcon>
                  <feature.icon />
                </FeatureIcon>
                <FeatureTitle>{feature.title}</FeatureTitle>
                <FeatureDescription>{feature.description}</FeatureDescription>
              </FeatureCard>
            ))}
          </FeaturesGrid>
        </SectionContent>
      </Section>

      <StatsSection>
        <SectionContent>
          <StatsGrid>
            {stats.map((stat, index) => (
              <StatCard key={index}>
                <StatNumber>{stat.number}</StatNumber>
                <StatLabel>{stat.label}</StatLabel>
              </StatCard>
            ))}
          </StatsGrid>
        </SectionContent>
      </StatsSection>

      <CTASection>
        <SectionContent>
          <CTATitle>Ready to Find Love?</CTATitle>
          <CTASubtitle>
            Join thousands of LDS singles who have found meaningful relationships and eternal companions through our platform.
          </CTASubtitle>
          <Button size="lg" onClick={() => navigate('/register')}>
            Create Your Profile Today
          </Button>
        </SectionContent>
      </CTASection>

      <Footer>
        <SectionContent>
          <p>&copy; 2024 LDS Hearts. Built with faith, love, and dedication to helping LDS singles find their eternal companions.</p>
        </SectionContent>
      </Footer>
    </LandingContainer>
  );
};

export default LandingPage;
