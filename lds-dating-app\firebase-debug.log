[debug] [2025-06-22T21:57:10.512Z] ----------------------------------------------------------------------
[debug] [2025-06-22T21:57:10.518Z] Command:       C:\Program Files\nodejs\node.exe C:\Users\<USER>\AppData\Roaming\npm\node_modules\firebase-tools\lib\bin\firebase.js init
[debug] [2025-06-22T21:57:10.518Z] CLI Version:   14.8.0
[debug] [2025-06-22T21:57:10.518Z] Platform:      win32
[debug] [2025-06-22T21:57:10.519Z] Node Version:  v22.14.0
[debug] [2025-06-22T21:57:10.519Z] Time:          Sun Jun 22 2025 18:57:10 GMT-0300 (<PERSON><PERSON><PERSON><PERSON>ras<PERSON>)
[debug] [2025-06-22T21:57:10.520Z] ----------------------------------------------------------------------
[debug] 
[debug] [2025-06-22T21:57:10.988Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-06-22T21:57:10.989Z] > authorizing via signed-in user (<EMAIL>)
[info] 
     ######## #### ########  ######## ########     ###     ######  ########
     ##        ##  ##     ## ##       ##     ##  ##   ##  ##       ##
     ######    ##  ########  ######   ########  #########  ######  ######
     ##        ##  ##    ##  ##       ##     ## ##     ##       ## ##
     ##       #### ##     ## ######## ########  ##     ##  ######  ########

You're about to initialize a Firebase project in this directory:

  C:\Users\<USER>\OneDrive\Documentos\Projetos\n8n\lds-dating-app

Before we get started, keep in mind:

  * You are initializing within an existing Firebase project directory

