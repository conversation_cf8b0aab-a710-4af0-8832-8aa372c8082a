rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    // Users can upload and read their own profile photos
    match /users/{userId}/photos/{allPaths=**} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
      // Allow other authenticated users to read photos
      allow read: if request.auth != null;
    }
    
    // Limit file size to 10MB and only allow image files
    match /{allPaths=**} {
      allow write: if request.auth != null &&
        request.resource.size < 10 * 1024 * 1024 &&
        request.resource.contentType.matches('image/.*');
    }
  }
}
