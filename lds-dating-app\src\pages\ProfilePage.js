import React, { useState } from 'react';
import styled from 'styled-components';
import { FiEdit, FiMapPin, FiHeart, FiCalendar, FiCamera, FiSettings } from 'react-icons/fi';
import { useAuth } from '../contexts/AuthContext';
import Button from '../components/UI/Button';
import Card from '../components/UI/Card';
import Avatar from '../components/UI/Avatar';
import Modal from '../components/UI/Modal';
import Input from '../components/UI/Input';

const ProfileContainer = styled.div`
  max-width: 800px;
  margin: 0 auto;
`;

const ProfileHeader = styled(Card)`
  padding: ${props => props.theme.spacing[8]};
  margin-bottom: ${props => props.theme.spacing[6]};
  text-align: center;
`;

const ProfileImageSection = styled.div`
  position: relative;
  margin-bottom: ${props => props.theme.spacing[6]};
`;

const ProfileImageGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: ${props => props.theme.spacing[3]};
  max-width: 300px;
  margin: 0 auto;
`;

const ProfileImage = styled.div`
  aspect-ratio: 1;
  background: ${props => props.src ? `url(${props.src}) center/cover` : props.theme.colors.gray[200]};
  border-radius: ${props => props.theme.borderRadius.xl};
  display: flex;
  align-items: center;
  justify-content: center;
  color: ${props => props.theme.colors.text.secondary};
  font-size: ${props => props.theme.fontSizes.xl};
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    transform: scale(1.02);
  }
  
  ${props => props.main && `
    grid-column: 1 / -1;
    aspect-ratio: 4/3;
  `}
`;

const ProfileName = styled.h1`
  font-size: ${props => props.theme.fontSizes['3xl']};
  font-weight: ${props => props.theme.fontWeights.bold};
  color: ${props => props.theme.colors.text.primary};
  margin-bottom: ${props => props.theme.spacing[2]};
`;

const ProfileAge = styled.span`
  font-size: ${props => props.theme.fontSizes.xl};
  color: ${props => props.theme.colors.text.secondary};
`;

const ProfileLocation = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  gap: ${props => props.theme.spacing[2]};
  color: ${props => props.theme.colors.text.secondary};
  margin-bottom: ${props => props.theme.spacing[4]};
`;

const ProfileActions = styled.div`
  display: flex;
  gap: ${props => props.theme.spacing[3]};
  justify-content: center;
`;

const InfoSection = styled(Card)`
  padding: ${props => props.theme.spacing[6]};
  margin-bottom: ${props => props.theme.spacing[6]};
`;

const SectionTitle = styled.h2`
  font-size: ${props => props.theme.fontSizes.xl};
  font-weight: ${props => props.theme.fontWeights.semibold};
  color: ${props => props.theme.colors.text.primary};
  margin-bottom: ${props => props.theme.spacing[4]};
  display: flex;
  align-items: center;
  gap: ${props => props.theme.spacing[2]};
`;

const InfoGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: ${props => props.theme.spacing[4]};
`;

const InfoItem = styled.div`
  display: flex;
  flex-direction: column;
  gap: ${props => props.theme.spacing[1]};
`;

const InfoLabel = styled.span`
  font-size: ${props => props.theme.fontSizes.sm};
  font-weight: ${props => props.theme.fontWeights.medium};
  color: ${props => props.theme.colors.text.secondary};
  text-transform: uppercase;
  letter-spacing: 0.5px;
`;

const InfoValue = styled.span`
  font-size: ${props => props.theme.fontSizes.base};
  color: ${props => props.theme.colors.text.primary};
`;

const Bio = styled.p`
  color: ${props => props.theme.colors.text.secondary};
  line-height: 1.6;
  margin-bottom: ${props => props.theme.spacing[4]};
`;

const InterestTags = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: ${props => props.theme.spacing[2]};
`;

const InterestTag = styled.span`
  background: ${props => props.theme.colors.primary}10;
  color: ${props => props.theme.colors.primary};
  padding: ${props => props.theme.spacing[2]} ${props => props.theme.spacing[3]};
  border-radius: ${props => props.theme.borderRadius.full};
  font-size: ${props => props.theme.fontSizes.sm};
  font-weight: ${props => props.theme.fontWeights.medium};
`;

const Quote = styled.div`
  text-align: center;
  margin-top: ${props => props.theme.spacing[8]};
  padding: ${props => props.theme.spacing[6]};
  background: linear-gradient(135deg, ${props => props.theme.colors.primary}10, ${props => props.theme.colors.accent}10);
  border-radius: ${props => props.theme.borderRadius.xl};
  border-left: 4px solid ${props => props.theme.colors.primary};
`;

const QuoteText = styled.p`
  font-style: italic;
  color: ${props => props.theme.colors.text.secondary};
  margin-bottom: ${props => props.theme.spacing[2]};
  font-size: ${props => props.theme.fontSizes.lg};
`;

const QuoteReference = styled.p`
  font-size: ${props => props.theme.fontSizes.sm};
  color: ${props => props.theme.colors.text.secondary};
  font-weight: ${props => props.theme.fontWeights.semibold};
`;

const ProfilePage = () => {
  const [editModalOpen, setEditModalOpen] = useState(false);
  const { userProfile } = useAuth();

  if (!userProfile) {
    return <div>Loading...</div>;
  }

  const ldsQuotes = [
    {
      text: "The family is ordained of God. Marriage between man and woman is essential to His eternal plan.",
      reference: "The Family: A Proclamation to the World"
    },
    {
      text: "Marriage is not just spiritual communion and passionate embraces; marriage is also three meals a day, sharing the workload and remembering to carry out the trash.",
      reference: "Dr. Joyce Brothers"
    },
    {
      text: "A successful marriage requires falling in love many times, always with the same person.",
      reference: "Mignon McLaughlin"
    }
  ];

  const randomQuote = ldsQuotes[Math.floor(Math.random() * ldsQuotes.length)];

  return (
    <ProfileContainer>
      <ProfileHeader>
        <ProfileImageSection>
          <ProfileImageGrid>
            <ProfileImage 
              main 
              src={userProfile.photos?.[0]}
            >
              {!userProfile.photos?.[0] && <FiCamera />}
            </ProfileImage>
            {[1, 2, 3].map(index => (
              <ProfileImage 
                key={index}
                src={userProfile.photos?.[index]}
              >
                {!userProfile.photos?.[index] && <FiCamera />}
              </ProfileImage>
            ))}
          </ProfileImageGrid>
        </ProfileImageSection>

        <ProfileName>
          {userProfile.displayName}
          <ProfileAge>, {userProfile.age}</ProfileAge>
        </ProfileName>
        
        <ProfileLocation>
          <FiMapPin />
          {userProfile.city}, {userProfile.country}
        </ProfileLocation>

        <ProfileActions>
          <Button onClick={() => setEditModalOpen(true)}>
            <FiEdit /> Edit Profile
          </Button>
          <Button variant="outline">
            <FiSettings /> Settings
          </Button>
        </ProfileActions>
      </ProfileHeader>

      <InfoSection>
        <SectionTitle>
          <FiHeart />
          About Me
        </SectionTitle>
        <Bio>{userProfile.bio}</Bio>
        
        <InfoGrid>
          <InfoItem>
            <InfoLabel>Ward</InfoLabel>
            <InfoValue>{userProfile.ward}</InfoValue>
          </InfoItem>
          <InfoItem>
            <InfoLabel>Stake</InfoLabel>
            <InfoValue>{userProfile.stake}</InfoValue>
          </InfoItem>
          <InfoItem>
            <InfoLabel>Activity Level</InfoLabel>
            <InfoValue>{userProfile.activityLevel?.replace('-', ' ')}</InfoValue>
          </InfoItem>
          <InfoItem>
            <InfoLabel>Looking For</InfoLabel>
            <InfoValue>{userProfile.lookingFor}</InfoValue>
          </InfoItem>
        </InfoGrid>
      </InfoSection>

      {userProfile.isReturnedMissionary && (
        <InfoSection>
          <SectionTitle>
            <FiCalendar />
            Mission Experience
          </SectionTitle>
          <InfoGrid>
            <InfoItem>
              <InfoLabel>Mission</InfoLabel>
              <InfoValue>{userProfile.missionLocation}</InfoValue>
            </InfoItem>
            <InfoItem>
              <InfoLabel>Years Served</InfoLabel>
              <InfoValue>{userProfile.missionYears}</InfoValue>
            </InfoItem>
          </InfoGrid>
        </InfoSection>
      )}

      {userProfile.interests && userProfile.interests.length > 0 && (
        <InfoSection>
          <SectionTitle>Interests</SectionTitle>
          <InterestTags>
            {userProfile.interests.map((interest, index) => (
              <InterestTag key={index}>{interest}</InterestTag>
            ))}
          </InterestTags>
        </InfoSection>
      )}

      <Quote>
        <QuoteText>"{randomQuote.text}"</QuoteText>
        <QuoteReference>— {randomQuote.reference}</QuoteReference>
      </Quote>

      <Modal
        isOpen={editModalOpen}
        onClose={() => setEditModalOpen(false)}
        title="Edit Profile"
        size="lg"
      >
        <p>Profile editing functionality will be implemented here.</p>
      </Modal>
    </ProfileContainer>
  );
};

export default ProfilePage;
