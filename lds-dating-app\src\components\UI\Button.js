import styled, { css } from 'styled-components';

const ButtonBase = styled.button`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: ${props => props.theme.spacing[2]};
  font-family: ${props => props.theme.fonts.primary};
  font-weight: ${props => props.theme.fontWeights.semibold};
  border-radius: ${props => props.theme.borderRadius.lg};
  transition: all 0.2s ease-in-out;
  cursor: pointer;
  text-decoration: none;
  border: none;
  outline: none;
  position: relative;
  overflow: hidden;

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  &:focus-visible {
    outline: 2px solid ${props => props.theme.colors.primary};
    outline-offset: 2px;
  }

  /* Size variants */
  ${props => props.size === 'sm' && css`
    padding: ${props.theme.spacing[2]} ${props.theme.spacing[3]};
    font-size: ${props.theme.fontSizes.sm};
    min-height: 32px;
  `}

  ${props => props.size === 'md' && css`
    padding: ${props.theme.spacing[3]} ${props.theme.spacing[4]};
    font-size: ${props.theme.fontSizes.base};
    min-height: 40px;
  `}

  ${props => props.size === 'lg' && css`
    padding: ${props.theme.spacing[4]} ${props.theme.spacing[6]};
    font-size: ${props.theme.fontSizes.lg};
    min-height: 48px;
  `}

  /* Variant styles */
  ${props => props.variant === 'primary' && css`
    background: linear-gradient(45deg, ${props.theme.colors.primary}, ${props.theme.colors.primaryLight});
    color: ${props.theme.colors.white};
    box-shadow: ${props.theme.shadows.sm};

    &:hover:not(:disabled) {
      transform: translateY(-1px);
      box-shadow: ${props.theme.shadows.md};
    }

    &:active {
      transform: translateY(0);
    }
  `}

  ${props => props.variant === 'secondary' && css`
    background: ${props.theme.colors.white};
    color: ${props.theme.colors.text.primary};
    border: 1px solid ${props.theme.colors.border.medium};

    &:hover:not(:disabled) {
      background: ${props.theme.colors.gray[50]};
      border-color: ${props.theme.colors.border.dark};
    }
  `}

  ${props => props.variant === 'outline' && css`
    background: transparent;
    color: ${props.theme.colors.primary};
    border: 1px solid ${props.theme.colors.primary};

    &:hover:not(:disabled) {
      background: ${props.theme.colors.primary};
      color: ${props.theme.colors.white};
    }
  `}

  ${props => props.variant === 'ghost' && css`
    background: transparent;
    color: ${props.theme.colors.text.primary};

    &:hover:not(:disabled) {
      background: ${props.theme.colors.gray[100]};
    }
  `}

  ${props => props.variant === 'danger' && css`
    background: ${props.theme.colors.error};
    color: ${props.theme.colors.white};

    &:hover:not(:disabled) {
      background: #c53030;
    }
  `}

  /* Full width */
  ${props => props.fullWidth && css`
    width: 100%;
  `}

  /* Loading state */
  ${props => props.loading && css`
    pointer-events: none;
    
    &::after {
      content: '';
      position: absolute;
      width: 16px;
      height: 16px;
      border: 2px solid transparent;
      border-top: 2px solid currentColor;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
  `}

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;

const Button = ({ 
  children, 
  variant = 'primary', 
  size = 'md', 
  fullWidth = false,
  loading = false,
  disabled = false,
  onClick,
  type = 'button',
  ...props 
}) => {
  return (
    <ButtonBase
      variant={variant}
      size={size}
      fullWidth={fullWidth}
      loading={loading}
      disabled={disabled || loading}
      onClick={onClick}
      type={type}
      {...props}
    >
      {!loading && children}
    </ButtonBase>
  );
};

export default Button;
